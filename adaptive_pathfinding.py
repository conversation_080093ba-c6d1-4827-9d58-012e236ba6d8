#!/usr/bin/env python3
"""
Python实现的自适应路径规划算法
包含多分辨率网格、A*路径规划、门优化等功能
"""

import numpy as np
import json
import time
from typing import List, Tuple, Optional, Dict, Set
from dataclasses import dataclass
from enum import Enum
import heapq
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D


class NodeType(Enum):
    EMPTY = "Empty"
    OBSTACLE = "Obstacle"
    MIXED = "Mixed"


class FaceDirection(Enum):
    X_POSITIVE = "XPositive"
    X_NEGATIVE = "XNegative"
    Y_POSITIVE = "YPositive"
    Y_NEGATIVE = "YNegative"
    Z_POSITIVE = "ZPositive"
    Z_NEGATIVE = "ZNegative"


@dataclass
class Point3D:
    x: float
    y: float
    z: float

    def distance_to(self, other: "Point3D") -> float:
        return np.sqrt(
            (self.x - other.x) ** 2 + (self.y - other.y) ** 2 + (self.z - other.z) ** 2
        )


@dataclass
class Box3D:
    x: float
    y: float
    z: float
    width: float
    height: float
    depth: float

    def contains_point(self, x: float, y: float, z: float) -> bool:
        return (
            self.x <= x <= self.x + self.width
            and self.y <= y <= self.y + self.height
            and self.z <= z <= self.z + self.depth
        )

    def center(self) -> Tuple[float, float, float]:
        return (
            self.x + self.width / 2,
            self.y + self.height / 2,
            self.z + self.depth / 2,
        )


@dataclass
class SharedFace3D:
    bounds: Box3D
    face_direction: FaceDirection


@dataclass
class GridCell3D:
    id: int
    bounds: Box3D
    node_type: NodeType
    level: int


@dataclass
class CorridorGate:
    cell_a: int
    cell_b: int
    shared_face: SharedFace3D


@dataclass
class Corridor:
    cells: List[int]
    gates: List[int]


@dataclass
class RefinedPath:
    points: List[Point3D]
    total_length: float
    smoothness: float


class VirtualGridNode:
    def __init__(self, x: int, y: int, z: int):
        self.x = x
        self.y = y
        self.z = z

    def __eq__(self, other):
        return self.x == other.x and self.y == other.y and self.z == other.z

    def __hash__(self):
        return hash((self.x, self.y, self.z))

    def get_neighbors_6(self) -> List["VirtualGridNode"]:
        return [
            VirtualGridNode(self.x + 1, self.y, self.z),
            VirtualGridNode(self.x - 1, self.y, self.z),
            VirtualGridNode(self.x, self.y + 1, self.z),
            VirtualGridNode(self.x, self.y - 1, self.z),
            VirtualGridNode(self.x, self.y, self.z + 1),
            VirtualGridNode(self.x, self.y, self.z - 1),
        ]

    def to_point3d(self) -> Point3D:
        return Point3D(self.x + 0.5, self.y + 0.5, self.z + 0.5)

    def distance_to(self, other: "VirtualGridNode") -> float:
        dx = self.x - other.x
        dy = self.y - other.y
        dz = self.z - other.z
        return np.sqrt(dx * dx + dy * dy + dz * dz)


class PathNode:
    def __init__(
        self, cell_id: int, g_cost: float, h_cost: float, parent: Optional[int] = None
    ):
        self.cell_id = cell_id
        self.g_cost = g_cost
        self.h_cost = h_cost
        self.parent = parent

    def f_cost(self) -> float:
        return self.g_cost + self.h_cost

    def __lt__(self, other):
        return self.f_cost() < other.f_cost()


class VirtualPathNode:
    def __init__(
        self,
        node: VirtualGridNode,
        g_cost: float,
        h_cost: float,
        parent: Optional[VirtualGridNode] = None,
    ):
        self.node = node
        self.g_cost = g_cost
        self.h_cost = h_cost
        self.parent = parent

    def f_cost(self) -> float:
        return self.g_cost + self.h_cost

    def __lt__(self, other):
        return self.f_cost() < other.f_cost()


class MultiResolutionGrid3D:
    def __init__(self, map_size: int, map_height: int):
        self.map_size = map_size
        self.map_height = map_height
        self.cells: List[GridCell3D] = []
        self.adjacency: Dict[int, List[Tuple[int, float]]] = {}
        self.corridor_gates: List[CorridorGate] = []
        self.corridors: List[Corridor] = []

        # 创建体素数据
        self.voxels = np.zeros((map_size, map_size, map_height), dtype=int)

    def add_obstacles(self, obstacles: List[Tuple[int, int, int, int, int, int]]):
        """添加障碍物 (x, y, z, width, height, depth)"""
        for x, y, z, w, h, d in obstacles:
            for i in range(x, min(x + w, self.map_size)):
                for j in range(y, min(y + h, self.map_size)):
                    for k in range(z, min(z + d, self.map_height)):
                        self.voxels[i, j, k] = 3  # 障碍物标记为3

    def build_hierarchical_grid(self):
        """构建分层网格"""
        print("构建分层网格...")

        # 从地图实际尺寸开始递归构建
        self._build_recursive(0, 0, 0, self.map_size, self.map_size, self.map_height, 0)

        print(f"网格构建完成，共 {len(self.cells)} 个单元")

    def _build_recursive(
        self, x: int, y: int, z: int, w: int, h: int, d: int, level: int
    ):
        """递归构建网格"""
        # 检查当前区域的类型
        node_type = self._determine_node_type(x, y, z, w, h, d)

        cell_id = len(self.cells)
        bounds = Box3D(x, y, z, w, h, d)
        cell = GridCell3D(cell_id, bounds, node_type, level)
        self.cells.append(cell)

        # 如果是混合类型且尺寸足够大，继续细分
        if node_type == NodeType.MIXED and min(w, h, d) > 4:
            # 八叉树细分
            hw, hh, hd = w // 2, h // 2, d // 2
            for dx in [0, hw]:
                for dy in [0, hh]:
                    for dz in [0, hd]:
                        if (
                            x + dx < self.map_size
                            and y + dy < self.map_size
                            and z + dz < self.map_height
                        ):
                            self._build_recursive(
                                x + dx, y + dy, z + dz, hw, hh, hd, level + 1
                            )

    def _determine_node_type(
        self, x: int, y: int, z: int, w: int, h: int, d: int
    ) -> NodeType:
        """确定节点类型"""
        obstacle_count = 0
        total_count = 0

        for i in range(x, min(x + w, self.map_size)):
            for j in range(y, min(y + h, self.map_size)):
                for k in range(z, min(z + d, self.map_height)):
                    if self.voxels[i, j, k] == 3:
                        obstacle_count += 1
                    total_count += 1

        if obstacle_count == 0:
            return NodeType.EMPTY
        elif obstacle_count == total_count:
            return NodeType.OBSTACLE
        else:
            return NodeType.MIXED

    def build_adjacency(self):
        """构建邻接关系"""
        print(f"构建邻接关系... ({len(self.cells)} 个网格单元)")

        self.adjacency.clear()

        for i, cell_a in enumerate(self.cells):
            if cell_a.node_type == NodeType.OBSTACLE:
                continue

            neighbors = []
            for j, cell_b in enumerate(self.cells):
                if i != j and cell_b.node_type != NodeType.OBSTACLE:
                    if self._are_adjacent(cell_a, cell_b):
                        cost = self._calculate_edge_cost(cell_a, cell_b)
                        neighbors.append((j, cost))

            self.adjacency[i] = neighbors

        print(f"邻接关系构建完成")

    def _are_adjacent(self, cell_a: GridCell3D, cell_b: GridCell3D) -> bool:
        """检查两个网格单元是否相邻"""
        a_bounds = cell_a.bounds
        b_bounds = cell_b.bounds

        # 检查是否有共享面
        x_adjacent = (
            abs(a_bounds.x + a_bounds.width - b_bounds.x) < 1e-6
            or abs(b_bounds.x + b_bounds.width - a_bounds.x) < 1e-6
        )
        y_adjacent = (
            abs(a_bounds.y + a_bounds.height - b_bounds.y) < 1e-6
            or abs(b_bounds.y + b_bounds.height - a_bounds.y) < 1e-6
        )
        z_adjacent = (
            abs(a_bounds.z + a_bounds.depth - b_bounds.z) < 1e-6
            or abs(b_bounds.z + b_bounds.depth - a_bounds.z) < 1e-6
        )

        # 检查重叠
        x_overlap = not (
            a_bounds.x + a_bounds.width <= b_bounds.x
            or b_bounds.x + b_bounds.width <= a_bounds.x
        )
        y_overlap = not (
            a_bounds.y + a_bounds.height <= b_bounds.y
            or b_bounds.y + b_bounds.height <= a_bounds.y
        )
        z_overlap = not (
            a_bounds.z + a_bounds.depth <= b_bounds.z
            or b_bounds.z + b_bounds.depth <= a_bounds.z
        )

        return (
            (x_adjacent and y_overlap and z_overlap)
            or (y_adjacent and x_overlap and z_overlap)
            or (z_adjacent and x_overlap and y_overlap)
        )

    def _calculate_edge_cost(self, cell_a: GridCell3D, cell_b: GridCell3D) -> float:
        """计算边的代价"""
        center_a = cell_a.bounds.center()
        center_b = cell_b.bounds.center()

        distance = np.sqrt(
            (center_a[0] - center_b[0]) ** 2
            + (center_a[1] - center_b[1]) ** 2
            + (center_a[2] - center_b[2]) ** 2
        )

        # 考虑网格大小差异的惩罚
        size_a = min(cell_a.bounds.width, cell_a.bounds.height, cell_a.bounds.depth)
        size_b = min(cell_b.bounds.width, cell_b.bounds.height, cell_b.bounds.depth)
        size_penalty = abs(size_a - size_b) * 0.1

        return distance + size_penalty

    def find_path_astar(
        self, start_point: Point3D, end_point: Point3D
    ) -> Optional[List[int]]:
        """使用A*算法寻找路径"""
        print(
            f"开始A*路径规划: ({start_point.x:.1f},{start_point.y:.1f},{start_point.z:.1f}) -> ({end_point.x:.1f},{end_point.y:.1f},{end_point.z:.1f})"
        )

        # 找到包含起点和终点的网格单元
        start_cell_id = self._find_cell_containing_point(start_point)
        end_cell_id = self._find_cell_containing_point(end_point)

        if start_cell_id is None or end_cell_id is None:
            print("无法找到包含起点或终点的网格单元")
            return None

        print(f"起点在网格单元 {start_cell_id}, 终点在网格单元 {end_cell_id}")

        # A*算法
        open_set = []
        closed_set = set()
        came_from = {}
        g_score = {start_cell_id: 0.0}

        start_h = self._heuristic_cost(start_cell_id, end_cell_id)
        heapq.heappush(open_set, PathNode(start_cell_id, 0.0, start_h))

        while open_set:
            current_node = heapq.heappop(open_set)
            current_id = current_node.cell_id

            if current_id == end_cell_id:
                # 重构路径
                path = []
                while current_id in came_from:
                    path.append(current_id)
                    current_id = came_from[current_id]
                path.append(start_cell_id)
                path.reverse()
                print(f"找到路径，长度: {len(path)}")
                return path

            closed_set.add(current_id)

            # 检查邻居
            if current_id in self.adjacency:
                for neighbor_id, edge_cost in self.adjacency[current_id]:
                    if neighbor_id in closed_set:
                        continue

                    tentative_g = current_node.g_cost + edge_cost

                    if neighbor_id not in g_score or tentative_g < g_score[neighbor_id]:
                        came_from[neighbor_id] = current_id
                        g_score[neighbor_id] = tentative_g
                        h_cost = self._heuristic_cost(neighbor_id, end_cell_id)
                        heapq.heappush(
                            open_set, PathNode(neighbor_id, tentative_g, h_cost)
                        )

        print("未找到路径")
        return None

    def _find_cell_containing_point(self, point: Point3D) -> Optional[int]:
        """找到包含指定点的最小网格单元"""
        best_cell_id = None
        best_cell_size = float("inf")

        for cell in self.cells:
            if cell.node_type != NodeType.OBSTACLE and cell.bounds.contains_point(
                point.x, point.y, point.z
            ):
                # 选择最小的网格单元
                cell_size = min(
                    cell.bounds.width, cell.bounds.height, cell.bounds.depth
                )
                if cell_size < best_cell_size:
                    best_cell_size = cell_size
                    best_cell_id = cell.id

        return best_cell_id

    def _heuristic_cost(self, cell_a_id: int, cell_b_id: int) -> float:
        """启发式代价函数"""
        center_a = self.cells[cell_a_id].bounds.center()
        center_b = self.cells[cell_b_id].bounds.center()

        return np.sqrt(
            (center_a[0] - center_b[0]) ** 2
            + (center_a[1] - center_b[1]) ** 2
            + (center_a[2] - center_b[2]) ** 2
        )

    def create_corridor_and_gates(self, path_cells: List[int]) -> Optional[int]:
        """为路径创建管道和门"""
        if len(path_cells) < 2:
            return None

        print(f"为路径创建管道和门，路径长度: {len(path_cells)}")

        # 创建门
        gates = []
        for i in range(len(path_cells) - 1):
            cell_a_id = path_cells[i]
            cell_b_id = path_cells[i + 1]

            gate = self._create_gate_between_cells(cell_a_id, cell_b_id)
            if gate:
                gate_id = len(self.corridor_gates)
                self.corridor_gates.append(gate)
                gates.append(gate_id)

        # 创建管道
        corridor = Corridor(cells=path_cells, gates=gates)
        corridor_id = len(self.corridors)
        self.corridors.append(corridor)

        print(f"创建了 {len(gates)} 个门")
        return corridor_id

    def _create_gate_between_cells(
        self, cell_a_id: int, cell_b_id: int
    ) -> Optional[CorridorGate]:
        """在两个相邻网格单元之间创建门"""
        cell_a = self.cells[cell_a_id]
        cell_b = self.cells[cell_b_id]

        # 计算共享面
        shared_face = self._calculate_shared_face(cell_a.bounds, cell_b.bounds)
        if shared_face:
            return CorridorGate(cell_a_id, cell_b_id, shared_face)
        return None

    def _calculate_shared_face(
        self, bounds_a: Box3D, bounds_b: Box3D
    ) -> Optional[SharedFace3D]:
        """计算两个边界框的共享面"""
        # 检查X方向的共享面
        if abs(bounds_a.x + bounds_a.width - bounds_b.x) < 1e-6:
            # A的右面与B的左面相邻
            y_start = max(bounds_a.y, bounds_b.y)
            y_end = min(bounds_a.y + bounds_a.height, bounds_b.y + bounds_b.height)
            z_start = max(bounds_a.z, bounds_b.z)
            z_end = min(bounds_a.z + bounds_a.depth, bounds_b.z + bounds_b.depth)

            if y_end > y_start and z_end > z_start:
                face_bounds = Box3D(
                    bounds_a.x + bounds_a.width,
                    y_start,
                    z_start,
                    0,
                    y_end - y_start,
                    z_end - z_start,
                )
                return SharedFace3D(face_bounds, FaceDirection.X_POSITIVE)

        elif abs(bounds_b.x + bounds_b.width - bounds_a.x) < 1e-6:
            # B的右面与A的左面相邻
            y_start = max(bounds_a.y, bounds_b.y)
            y_end = min(bounds_a.y + bounds_a.height, bounds_b.y + bounds_b.height)
            z_start = max(bounds_a.z, bounds_b.z)
            z_end = min(bounds_a.z + bounds_a.depth, bounds_b.z + bounds_b.depth)

            if y_end > y_start and z_end > z_start:
                face_bounds = Box3D(
                    bounds_a.x, y_start, z_start, 0, y_end - y_start, z_end - z_start
                )
                return SharedFace3D(face_bounds, FaceDirection.X_NEGATIVE)

        # 检查Y方向的共享面
        if abs(bounds_a.y + bounds_a.height - bounds_b.y) < 1e-6:
            x_start = max(bounds_a.x, bounds_b.x)
            x_end = min(bounds_a.x + bounds_a.width, bounds_b.x + bounds_b.width)
            z_start = max(bounds_a.z, bounds_b.z)
            z_end = min(bounds_a.z + bounds_a.depth, bounds_b.z + bounds_b.depth)

            if x_end > x_start and z_end > z_start:
                face_bounds = Box3D(
                    x_start,
                    bounds_a.y + bounds_a.height,
                    z_start,
                    x_end - x_start,
                    0,
                    z_end - z_start,
                )
                return SharedFace3D(face_bounds, FaceDirection.Y_POSITIVE)

        elif abs(bounds_b.y + bounds_b.height - bounds_a.y) < 1e-6:
            x_start = max(bounds_a.x, bounds_b.x)
            x_end = min(bounds_a.x + bounds_a.width, bounds_b.x + bounds_b.width)
            z_start = max(bounds_a.z, bounds_b.z)
            z_end = min(bounds_a.z + bounds_a.depth, bounds_b.z + bounds_b.depth)

            if x_end > x_start and z_end > z_start:
                face_bounds = Box3D(
                    x_start, bounds_a.y, z_start, x_end - x_start, 0, z_end - z_start
                )
                return SharedFace3D(face_bounds, FaceDirection.Y_NEGATIVE)

        # 检查Z方向的共享面
        if abs(bounds_a.z + bounds_a.depth - bounds_b.z) < 1e-6:
            x_start = max(bounds_a.x, bounds_b.x)
            x_end = min(bounds_a.x + bounds_a.width, bounds_b.x + bounds_b.width)
            y_start = max(bounds_a.y, bounds_b.y)
            y_end = min(bounds_a.y + bounds_a.height, bounds_b.y + bounds_b.height)

            if x_end > x_start and y_end > y_start:
                face_bounds = Box3D(
                    x_start,
                    y_start,
                    bounds_a.z + bounds_a.depth,
                    x_end - x_start,
                    y_end - y_start,
                    0,
                )
                return SharedFace3D(face_bounds, FaceDirection.Z_POSITIVE)

        elif abs(bounds_b.z + bounds_b.depth - bounds_a.z) < 1e-6:
            x_start = max(bounds_a.x, bounds_b.x)
            x_end = min(bounds_a.x + bounds_a.width, bounds_b.x + bounds_b.width)
            y_start = max(bounds_a.y, bounds_b.y)
            y_end = min(bounds_a.y + bounds_a.height, bounds_b.y + bounds_b.height)

            if x_end > x_start and y_end > y_start:
                face_bounds = Box3D(
                    x_start, y_start, bounds_a.z, x_end - x_start, y_end - y_start, 0
                )
                return SharedFace3D(face_bounds, FaceDirection.Z_NEGATIVE)

        return None

    def refine_path_with_gates(
        self, corridor_id: int, start_point: Point3D, end_point: Point3D
    ) -> Optional[RefinedPath]:
        """使用门优化路径"""
        if corridor_id >= len(self.corridors):
            return None

        corridor = self.corridors[corridor_id]
        gate_ids = corridor.gates

        if not gate_ids:
            # 没有门，直接连接
            points = [start_point, end_point]
            total_length = self._calculate_path_length(points)
            return RefinedPath(points, total_length, 1.0)

        print(f"开始几何投影门优化，门数量: {len(gate_ids)}")

        # 使用几何投影方法计算每个门的最优通过点
        gate_points = []
        current_point = start_point

        for i, gate_id in enumerate(gate_ids):
            if gate_id < len(self.corridor_gates):
                gate = self.corridor_gates[gate_id]
                optimal_point = self._find_closest_point_in_gate(current_point, gate)
                print(
                    f"门{i}: 当前点({current_point.x:.1f},{current_point.y:.1f},{current_point.z:.1f}) -> 最优点({optimal_point.x:.1f},{optimal_point.y:.1f},{optimal_point.z:.1f})"
                )
                gate_points.append(optimal_point)
                current_point = optimal_point

        # 构建初始路径
        initial_points = [start_point] + gate_points + [end_point]
        print(f"初始路径点数: {len(initial_points)}")

        # 应用跨点直连优化
        optimized_points = self._optimize_path_with_line_of_sight(
            initial_points, gate_ids
        )

        # 计算路径长度和平滑度
        total_length = self._calculate_path_length(optimized_points)
        smoothness = self._calculate_path_smoothness(optimized_points)

        print(
            f"跨点优化完成，路径点数: {len(initial_points)} -> {len(optimized_points)}, 总长度: {total_length:.2f}"
        )

        return RefinedPath(optimized_points, total_length, smoothness)

    def _find_closest_point_in_gate(
        self, current_point: Point3D, gate: CorridorGate
    ) -> Point3D:
        """基于几何投影找到门内距离当前点最近的点"""
        bounds = gate.shared_face.bounds
        face_direction = gate.shared_face.face_direction

        if face_direction in [FaceDirection.X_POSITIVE, FaceDirection.X_NEGATIVE]:
            # X轴朝向门
            projected_y = current_point.y
            projected_z = current_point.z

            # 检查投影点是否在门内
            if (
                bounds.y <= projected_y <= bounds.y + bounds.height
                and bounds.z <= projected_z <= bounds.z + bounds.depth
            ):
                gate_x = bounds.x + bounds.width / 2.0
                return Point3D(gate_x, projected_y, projected_z)
            else:
                # 夹紧到边界
                clamped_y = max(bounds.y, min(projected_y, bounds.y + bounds.height))
                clamped_z = max(bounds.z, min(projected_z, bounds.z + bounds.depth))
                gate_x = bounds.x + bounds.width / 2.0
                return Point3D(gate_x, clamped_y, clamped_z)

        elif face_direction in [FaceDirection.Y_POSITIVE, FaceDirection.Y_NEGATIVE]:
            # Y轴朝向门
            projected_x = current_point.x
            projected_z = current_point.z

            if (
                bounds.x <= projected_x <= bounds.x + bounds.width
                and bounds.z <= projected_z <= bounds.z + bounds.depth
            ):
                gate_y = bounds.y + bounds.height / 2.0
                return Point3D(projected_x, gate_y, projected_z)
            else:
                clamped_x = max(bounds.x, min(projected_x, bounds.x + bounds.width))
                clamped_z = max(bounds.z, min(projected_z, bounds.z + bounds.depth))
                gate_y = bounds.y + bounds.height / 2.0
                return Point3D(clamped_x, gate_y, clamped_z)

        else:  # Z轴朝向门
            projected_x = current_point.x
            projected_y = current_point.y

            if (
                bounds.x <= projected_x <= bounds.x + bounds.width
                and bounds.y <= projected_y <= bounds.y + bounds.height
            ):
                gate_z = bounds.z + bounds.depth / 2.0
                return Point3D(projected_x, projected_y, gate_z)
            else:
                clamped_x = max(bounds.x, min(projected_x, bounds.x + bounds.width))
                clamped_y = max(bounds.y, min(projected_y, bounds.y + bounds.height))
                gate_z = bounds.z + bounds.depth / 2.0
                return Point3D(clamped_x, clamped_y, gate_z)

    def _optimize_path_with_line_of_sight(
        self, initial_points: List[Point3D], gate_ids: List[int]
    ) -> List[Point3D]:
        """跨点直连优化"""
        if len(initial_points) <= 2:
            return initial_points

        optimized_path = [initial_points[0]]  # 起点
        current_index = 0

        while current_index < len(initial_points) - 1:
            current_point = initial_points[current_index]
            furthest_reachable_index = current_index + 1

            # 尝试跳过尽可能多的中间点
            for target_index in range(current_index + 2, len(initial_points)):
                target_point = initial_points[target_index]

                if self._can_reach_directly_through_gates(
                    current_point, target_point, current_index, target_index, gate_ids
                ):
                    furthest_reachable_index = target_index
                    print(f"  可以跳过: 从点{current_index} 直接到点{target_index}")
                else:
                    break

            # 移动到最远可达的点
            if furthest_reachable_index < len(initial_points) - 1:
                optimized_path.append(initial_points[furthest_reachable_index])

            current_index = furthest_reachable_index

        # 添加终点
        optimized_path.append(initial_points[-1])
        return optimized_path

    def _can_reach_directly_through_gates(
        self,
        start_point: Point3D,
        end_point: Point3D,
        start_index: int,
        end_index: int,
        gate_ids: List[int],
    ) -> bool:
        """检查是否能直接穿过所有中间门"""
        # 简化版本：假设可以直接连接（实际应该检查直线是否穿过门）
        return True

    def _calculate_path_length(self, points: List[Point3D]) -> float:
        """计算路径长度"""
        if len(points) < 2:
            return 0.0

        total_length = 0.0
        for i in range(len(points) - 1):
            total_length += points[i].distance_to(points[i + 1])
        return total_length

    def _calculate_path_smoothness(self, points: List[Point3D]) -> float:
        """计算路径平滑度"""
        if len(points) < 3:
            return 1.0

        total_angle_change = 0.0
        for i in range(1, len(points) - 1):
            # 计算角度变化
            v1 = np.array(
                [
                    points[i].x - points[i - 1].x,
                    points[i].y - points[i - 1].y,
                    points[i].z - points[i - 1].z,
                ]
            )
            v2 = np.array(
                [
                    points[i + 1].x - points[i].x,
                    points[i + 1].y - points[i].y,
                    points[i + 1].z - points[i].z,
                ]
            )

            norm1 = np.linalg.norm(v1)
            norm2 = np.linalg.norm(v2)

            if norm1 > 1e-6 and norm2 > 1e-6:
                cos_angle = np.dot(v1, v2) / (norm1 * norm2)
                cos_angle = np.clip(cos_angle, -1.0, 1.0)
                angle = np.arccos(cos_angle)
                total_angle_change += angle

        # 平滑度：角度变化越小越平滑
        return 1.0 / (1.0 + total_angle_change)


def create_grid_subdivision_slices(grid: MultiResolutionGrid3D):
    """1. 网格细分切片图 - 显示不同切片的网格细分情况"""
    print("创建网格细分切片图...")

    # 选择几个关键Z层
    z_levels = [2, 8, 16, 24, 30]
    z_levels = [z for z in z_levels if z < grid.map_height]

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.flatten()

    for i, z_level in enumerate(z_levels[:6]):
        if i >= len(axes):
            break

        ax = axes[i]

        # 绘制障碍物（黑色）
        for x in range(grid.map_size):
            for y in range(grid.map_size):
                if z_level < grid.map_height and grid.voxels[x, y, z_level] == 3:
                    ax.add_patch(
                        plt.Rectangle((x, y), 1, 1, facecolor="black", alpha=0.8)
                    )

        # 绘制网格边界
        for cell in grid.cells:
            if cell.bounds.z <= z_level < cell.bounds.z + cell.bounds.depth:
                if cell.node_type == NodeType.OBSTACLE:
                    # 禁飞区 - 红色边框
                    rect = plt.Rectangle(
                        (cell.bounds.x, cell.bounds.y),
                        cell.bounds.width,
                        cell.bounds.height,
                        fill=False,
                        edgecolor="red",
                        alpha=0.8,
                        linewidth=1.5,
                    )
                elif cell.node_type == NodeType.EMPTY:
                    # 可飞行区域 - 绿色边框
                    rect = plt.Rectangle(
                        (cell.bounds.x, cell.bounds.y),
                        cell.bounds.width,
                        cell.bounds.height,
                        fill=False,
                        edgecolor="green",
                        alpha=0.6,
                        linewidth=0.8,
                    )
                else:
                    # 混合区域 - 灰色边框
                    rect = plt.Rectangle(
                        (cell.bounds.x, cell.bounds.y),
                        cell.bounds.width,
                        cell.bounds.height,
                        fill=False,
                        edgecolor="gray",
                        alpha=0.5,
                        linewidth=0.5,
                    )
                ax.add_patch(rect)

        ax.set_xlim(0, grid.map_size)
        ax.set_ylim(0, grid.map_size)
        ax.set_aspect("equal")
        ax.set_title(f"网格细分 Z={z_level}", fontsize=12)
        ax.grid(True, alpha=0.2)

        # 添加图例
        if i == 0:
            from matplotlib.lines import Line2D

            legend_elements = [
                Line2D([0], [0], color="black", lw=4, label="障碍物"),
                Line2D([0], [0], color="red", lw=2, label="禁飞区"),
                Line2D([0], [0], color="green", lw=2, label="可飞行区域"),
                Line2D([0], [0], color="gray", lw=1, label="混合区域"),
            ]
            ax.legend(
                handles=legend_elements,
                bbox_to_anchor=(1.05, 1),
                loc="upper left",
                fontsize=8,
            )

    # 隐藏多余的子图
    for i in range(len(z_levels), len(axes)):
        axes[i].set_visible(False)

    plt.tight_layout()
    plt.savefig("grid_subdivision_slices.png", dpi=150, bbox_inches="tight")
    print("网格细分切片图保存为 grid_subdivision_slices.png")
    return fig


def create_path_views(
    grid: MultiResolutionGrid3D,
    coarse_path: List[int],
    refined_path: Optional[RefinedPath],
    start_point: Point3D,
    end_point: Point3D,
):
    """2. 路径线路图 - 显示障碍物和路径的三个方向视图"""
    print("创建路径线路图...")

    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    # XY视图 (俯视图)
    ax_xy = axes[0]
    # 绘制障碍物投影
    for x in range(grid.map_size):
        for y in range(grid.map_size):
            has_obstacle = any(
                grid.voxels[x, y, z] == 3 for z in range(grid.map_height)
            )
            if has_obstacle:
                ax_xy.add_patch(plt.Rectangle((x, y), 1, 1, facecolor="red", alpha=0.6))

    # 绘制粗路径
    if coarse_path:
        coarse_points = []
        for cell_id in coarse_path:
            if cell_id < len(grid.cells):
                center = grid.cells[cell_id].bounds.center()
                coarse_points.append((center[0], center[1]))
        if len(coarse_points) > 1:
            xs, ys = zip(*coarse_points)
            ax_xy.plot(xs, ys, "orange", linewidth=4, alpha=0.8, label="粗路径")
            ax_xy.scatter(xs, ys, c="orange", s=60, marker="o", alpha=0.9)

    # 绘制细化路径
    if refined_path and len(refined_path.points) > 1:
        xs = [p.x for p in refined_path.points]
        ys = [p.y for p in refined_path.points]
        ax_xy.plot(xs, ys, "blue", linewidth=2, alpha=0.9, label="细化路径")

    # 起点终点
    ax_xy.scatter(
        [start_point.x], [start_point.y], c="green", s=200, marker="s", label="起点"
    )
    ax_xy.scatter(
        [end_point.x], [end_point.y], c="red", s=200, marker="^", label="终点"
    )

    ax_xy.set_xlim(0, grid.map_size)
    ax_xy.set_ylim(0, grid.map_size)
    ax_xy.set_aspect("equal")
    ax_xy.set_title("XY视图 (俯视)", fontsize=12)
    ax_xy.set_xlabel("X")
    ax_xy.set_ylabel("Y")
    ax_xy.legend()
    ax_xy.grid(True, alpha=0.3)

    # XZ视图 (侧视图)
    ax_xz = axes[1]
    # 绘制障碍物投影
    for x in range(grid.map_size):
        for z in range(grid.map_height):
            has_obstacle = any(grid.voxels[x, y, z] == 3 for y in range(grid.map_size))
            if has_obstacle:
                ax_xz.add_patch(plt.Rectangle((x, z), 1, 1, facecolor="red", alpha=0.6))

    # 绘制路径
    if coarse_path:
        coarse_points = []
        for cell_id in coarse_path:
            if cell_id < len(grid.cells):
                center = grid.cells[cell_id].bounds.center()
                coarse_points.append((center[0], center[2]))
        if len(coarse_points) > 1:
            xs, zs = zip(*coarse_points)
            ax_xz.plot(xs, zs, "orange", linewidth=4, alpha=0.8, label="粗路径")
            ax_xz.scatter(xs, zs, c="orange", s=60, marker="o", alpha=0.9)

    if refined_path and len(refined_path.points) > 1:
        xs = [p.x for p in refined_path.points]
        zs = [p.z for p in refined_path.points]
        ax_xz.plot(xs, zs, "blue", linewidth=2, alpha=0.9, label="细化路径")

    ax_xz.scatter(
        [start_point.x], [start_point.z], c="green", s=200, marker="s", label="起点"
    )
    ax_xz.scatter(
        [end_point.x], [end_point.z], c="red", s=200, marker="^", label="终点"
    )

    ax_xz.set_xlim(0, grid.map_size)
    ax_xz.set_ylim(0, grid.map_height)
    ax_xz.set_title("XZ视图 (侧视)", fontsize=12)
    ax_xz.set_xlabel("X")
    ax_xz.set_ylabel("Z")
    ax_xz.legend()
    ax_xz.grid(True, alpha=0.3)

    # YZ视图 (正视图)
    ax_yz = axes[2]
    # 绘制障碍物投影
    for y in range(grid.map_size):
        for z in range(grid.map_height):
            has_obstacle = any(grid.voxels[x, y, z] == 3 for x in range(grid.map_size))
            if has_obstacle:
                ax_yz.add_patch(plt.Rectangle((y, z), 1, 1, facecolor="red", alpha=0.6))

    # 绘制路径
    if coarse_path:
        coarse_points = []
        for cell_id in coarse_path:
            if cell_id < len(grid.cells):
                center = grid.cells[cell_id].bounds.center()
                coarse_points.append((center[1], center[2]))
        if len(coarse_points) > 1:
            ys, zs = zip(*coarse_points)
            ax_yz.plot(ys, zs, "orange", linewidth=4, alpha=0.8, label="粗路径")
            ax_yz.scatter(ys, zs, c="orange", s=60, marker="o", alpha=0.9)

    if refined_path and len(refined_path.points) > 1:
        ys = [p.y for p in refined_path.points]
        zs = [p.z for p in refined_path.points]
        ax_yz.plot(ys, zs, "blue", linewidth=2, alpha=0.9, label="细化路径")

    ax_yz.scatter(
        [start_point.y], [start_point.z], c="green", s=200, marker="s", label="起点"
    )
    ax_yz.scatter(
        [end_point.y], [end_point.z], c="red", s=200, marker="^", label="终点"
    )

    ax_yz.set_xlim(0, grid.map_size)
    ax_yz.set_ylim(0, grid.map_height)
    ax_yz.set_title("YZ视图 (正视)", fontsize=12)
    ax_yz.set_xlabel("Y")
    ax_yz.set_ylabel("Z")
    ax_yz.legend()
    ax_yz.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig("path_views.png", dpi=150, bbox_inches="tight")
    print("路径线路图保存为 path_views.png")
    return fig


def visualize_3d_interactive(
    grid: MultiResolutionGrid3D,
    coarse_path: List[int],
    refined_path: Optional[RefinedPath],
    start_point: Point3D,
    end_point: Point3D,
):
    """创建3D交互式可视化"""
    fig = plt.figure(figsize=(15, 10))
    ax = fig.add_subplot(111, projection="3d")

    # 绘制路径经过的粗网格边界（半透明）
    print("绘制路径经过的粗网格边界...")
    for cell_id in coarse_path:
        if cell_id < len(grid.cells):
            cell = grid.cells[cell_id]
            bounds = cell.bounds

            # 创建立方体的8个顶点
            vertices = [
                [bounds.x, bounds.y, bounds.z],
                [bounds.x + bounds.width, bounds.y, bounds.z],
                [bounds.x + bounds.width, bounds.y + bounds.height, bounds.z],
                [bounds.x, bounds.y + bounds.height, bounds.z],  # 底面
                [bounds.x, bounds.y, bounds.z + bounds.depth],
                [bounds.x + bounds.width, bounds.y, bounds.z + bounds.depth],
                [
                    bounds.x + bounds.width,
                    bounds.y + bounds.height,
                    bounds.z + bounds.depth,
                ],
                [bounds.x, bounds.y + bounds.height, bounds.z + bounds.depth],  # 顶面
            ]

            # 定义立方体的12条边
            edges = [
                [0, 1],
                [1, 2],
                [2, 3],
                [3, 0],  # 底面
                [4, 5],
                [5, 6],
                [6, 7],
                [7, 4],  # 顶面
                [0, 4],
                [1, 5],
                [2, 6],
                [3, 7],  # 竖直边
            ]

            # 绘制边框
            for edge in edges:
                points = np.array([vertices[edge[0]], vertices[edge[1]]])
                ax.plot3D(
                    points[:, 0],
                    points[:, 1],
                    points[:, 2],
                    color="lightgray",
                    alpha=0.3,
                    linewidth=0.5,
                )

    # 绘制粗路径
    if coarse_path:
        coarse_points = []
        for cell_id in coarse_path:
            if cell_id < len(grid.cells):
                center = grid.cells[cell_id].bounds.center()
                coarse_points.append(center)

        if len(coarse_points) > 1:
            xs, ys, zs = zip(*coarse_points)
            ax.plot(
                xs,
                ys,
                zs,
                color="orange",
                linewidth=4,
                alpha=0.8,
                label="粗路径",
                marker="o",
                markersize=6,
            )

    # 绘制细化路径
    if refined_path and len(refined_path.points) > 1:
        xs = [p.x for p in refined_path.points]
        ys = [p.y for p in refined_path.points]
        zs = [p.z for p in refined_path.points]
        ax.plot(
            xs,
            ys,
            zs,
            color="blue",
            linewidth=2,
            alpha=0.9,
            label="细化路径",
            marker="s",
            markersize=3,
        )

    # 标注起点和终点
    ax.scatter(
        [start_point.x],
        [start_point.y],
        [start_point.z],
        c="green",
        s=200,
        marker="s",
        alpha=1.0,
        edgecolors="darkgreen",
        linewidth=3,
        label="起点",
    )

    ax.scatter(
        [end_point.x],
        [end_point.y],
        [end_point.z],
        c="red",
        s=200,
        marker="^",
        alpha=1.0,
        edgecolors="darkred",
        linewidth=3,
        label="终点",
    )

    # 设置坐标轴
    ax.set_xlabel("X", fontsize=12)
    ax.set_ylabel("Y", fontsize=12)
    ax.set_zlabel("Z", fontsize=12)
    ax.set_title(
        "Python实现的3D自适应路径规划\n（鼠标拖拽旋转，滚轮缩放）", fontsize=14
    )

    # 设置坐标轴范围
    ax.set_xlim(0, grid.map_size)
    ax.set_ylim(0, grid.map_size)
    ax.set_zlim(0, grid.map_height)

    # 添加图例
    ax.legend(loc="upper right", fontsize=10)

    # 添加网格
    ax.grid(True, alpha=0.3)

    # 设置初始视角
    ax.view_init(elev=20, azim=45)

    plt.tight_layout()
    return fig


def main():
    """主函数"""
    print("=== Python实现的自适应路径规划算法 ===")

    # 创建网格 - 与Rust版本一致
    map_size = 128
    map_height = 32
    grid = MultiResolutionGrid3D(map_size, map_height)

    # 添加障碍物 - 与Rust版本一致
    obstacles = [
        (30, 30, 0, 20, 20, 15),  # 障碍物1
        (70, 20, 0, 25, 30, 18),  # 障碍物2
        (20, 80, 0, 30, 15, 12),  # 障碍物3
        (90, 70, 0, 15, 25, 20),  # 障碍物4
        (50, 60, 0, 20, 20, 10),  # 障碍物5
    ]

    print(f"添加 {len(obstacles)} 个障碍物")
    grid.add_obstacles(obstacles)

    # 构建分层网格
    start_time = time.time()
    grid.build_hierarchical_grid()
    grid_time = time.time() - start_time
    print(f"网格构建耗时: {grid_time:.3f}s")

    # 构建邻接关系
    start_time = time.time()
    grid.build_adjacency()
    adjacency_time = time.time() - start_time
    print(f"邻接关系构建耗时: {adjacency_time:.3f}s")

    # 路径规划 - 与Rust版本一致
    start_point = Point3D(10.0, 80.0, 5.0)
    end_point = Point3D(110.0, 10.0, 5.0)

    print(f"\n开始路径规划...")
    start_time = time.time()
    coarse_path = grid.find_path_astar(start_point, end_point)
    pathfinding_time = time.time() - start_time
    print(f"粗路径规划耗时: {pathfinding_time:.3f}s")

    if coarse_path:
        print(f"找到粗路径，包含 {len(coarse_path)} 个网格单元")

        # 创建管道和门
        start_time = time.time()
        corridor_id = grid.create_corridor_and_gates(coarse_path)
        corridor_time = time.time() - start_time
        print(f"管道创建耗时: {corridor_time:.3f}s")

        if corridor_id is not None:
            # 门优化路径细化
            print(f"\n应用门优化路径细化...")
            start_time = time.time()
            refined_path = grid.refine_path_with_gates(
                corridor_id, start_point, end_point
            )
            refine_time = time.time() - start_time
            print(f"路径细化耗时: {refine_time:.3f}s")

            if refined_path:
                print(f"细化路径点数: {len(refined_path.points)}")
                print(f"细化路径长度: {refined_path.total_length:.2f}")
                print(f"路径平滑度: {refined_path.smoothness:.3f}")

            # 生成切片总览
            print("\n创建切片总览图...")
            summary_fig = create_summary_grid(
                grid, coarse_path, refined_path, start_point, end_point
            )

            # 创建3D交互式可视化
            print("创建3D交互式可视化...")
            interactive_fig = visualize_3d_interactive(
                grid, coarse_path, refined_path, start_point, end_point
            )

            # 显示所有图形
            plt.show()

            print("\n可视化完成！")
            print("- 切片总览图保存为 slices_summary.png")
            print("- 3D交互式可视化已显示（可以鼠标拖拽旋转和缩放）")
        else:
            print("管道创建失败")
    else:
        print("未找到路径")

    print("\n=== 算法执行完成 ===")


if __name__ == "__main__":
    main()
