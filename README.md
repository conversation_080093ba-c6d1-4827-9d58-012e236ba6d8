# 3D自适应路径规划系统

这是一个从2D扩展到3D的自适应多分辨率网格路径规划系统，支持多种类型的3D障碍物和高效的路径规划算法。

## 功能特点

### 3D数据结构
- **Point3D**: 三维点坐标 (x, y, z)
- **Box3D**: 三维长方体障碍物
- **Obstacle3D**: 支持多种3D障碍物类型：
  - 长方体 (Box): 建筑物
  - 圆柱体 (Cylinder): 塔楼、柱子
  - 棱柱 (Prism): 不规则建筑，底面为多边形
  - 3D点集 (PointSet): 山体、岩石群等不规则障碍物

### 3D网格系统
- **八叉树结构**: 每个节点可细分为8个子节点
- **自适应细分**: 根据障碍物分布自动调整网格分辨率
- **多分辨率**: 支持从粗网格到1x1x1精细网格的多层次表示
- **高效存储**: 相比均匀网格大幅减少内存使用

### 3D路径规划
- **A*算法**: 3D空间中的最优路径搜索
- **6邻接**: 支持上下左右前后6个方向的移动
- **启发式函数**: 使用3D欧几里得距离

### 可视化系统
- **体素化**: 将3D空间转换为体素表示
- **颜色编码**: 
  - 绿色: 空旷区域网格
  - 红色: 被占据区域网格
  - 黑色: 障碍物本身
- **网格边框**: 显示多分辨率网格边界
- **Python可视化**: 使用matplotlib进行3D渲染

## 使用方法

### 1. 编译和运行Rust程序

```bash
# 安装依赖
cargo build --release

# 运行程序生成3D数据
cargo run --release
```

程序将生成 `voxel_data_3d.json` 文件，包含所有3D网格和障碍物数据。

### 2. Python可视化

确保安装了Python依赖：

```bash
pip install matplotlib numpy
```

运行可视化脚本：

```bash
python visualize_3d.py
```

### 3. 输出文件

- `voxel_data_3d.json`: 3D体素数据，包含网格信息和障碍物
- `3d_visualization_full.png`: 完整3D视图
- `3d_visualization_with_slice.png`: 带Z轴切片的3D视图
- `temp_slice_*.png`: Z轴各层切片图像

## 技术实现

### 几何计算优化
- **棱柱检测**: 复用2D多边形算法 + 高度判断
- **圆柱检测**: 2D圆形检测 + 高度范围检查
- **体积计算**: 采样法近似计算重叠体积

### 性能优化
- **自适应网格**: 只在需要的地方细分网格
- **邻接关系缓存**: 预计算网格单元间的连接关系
- **体素化优化**: 高效的3D空间到体素转换

### 数据导出
- **JSON格式**: 便于Python处理的结构化数据
- **体素数组**: 128x128x128的3D数组表示
- **网格边界**: 用于可视化网格结构的边界信息

## 示例场景

程序默认创建一个128x128x128的3D城市场景，包含：

1. **建筑物** (长方体): 10个不同高度的建筑
2. **塔楼** (圆柱体): 3个不同规模的圆形建筑
3. **不规则建筑** (棱柱): 三角形和方形底面的建筑
4. **地形特征** (3D点集): 模拟山体和岩石群

## 扩展性

### 添加新的障碍物类型
在 `Obstacle3D` 枚举中添加新变体，并实现相应的几何检测方法。

### 自定义启发式函数
修改 `heuristic` 方法以适应特定的路径规划需求。

### 动态障碍物
系统支持运行时添加障碍物并局部更新网格结构。

## 性能统计

对于128x128x128的地图：
- **理论最大网格数**: 2,097,152个1x1x1网格
- **实际网格数**: 通常减少90%以上
- **内存使用**: 大幅降低
- **路径规划速度**: 毫秒级响应

## 技术细节

### 八叉树细分规则
```
当前节点细分为8个子节点：
- (x, y, z) -> (x+w/2, y, z)
- (x, y, z) -> (x, y+h/2, z)  
- (x, y, z) -> (x, y, z+d/2)
- 以此类推...
```

### 邻接关系判断
两个3D网格单元相邻当且仅当它们共享一个面（XY面、XZ面或YZ面）。

### 体素化算法
将连续的3D空间离散化为固定大小的体素，每个体素记录其类型（空、网格、障碍物）。

## 未来改进

1. **GPU加速**: 使用CUDA或OpenCL加速体素化和路径搜索
2. **动态路径规划**: 支持移动障碍物的实时路径更新
3. **多目标路径规划**: 同时规划多个智能体的路径
4. **时空路径规划**: 加入时间维度，支持4D路径规划
