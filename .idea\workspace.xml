<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="CargoProjects">
    <cargoProject FILE="$PROJECT_DIR$/Cargo.toml">
      <package file="$PROJECT_DIR$">
        <enabledFeature name="default" />
      </package>
    </cargoProject>
  </component>
  <component name="ChangeListManager">
    <list default="true" id="854d443b-fe88-47be-9bbe-9b02ac349026" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ExecutionTargetManager" SELECTED_TARGET="RsBuildProfile:dev" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MacroExpansionManager">
    <option name="directoryName" value="Jt3O3amP" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zwb7QeBxvnvlTCJGXgIrokVhTK" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Cargo.Run adaptive_pathfinding.executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.rust.reset.selective.auto.import&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/crscu/route_palnning/codes/rust/adaptive_pathfinding&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;org.rust.cargo.project.model.PROJECT_DISCOVERY&quot;: &quot;true&quot;,
    &quot;org.rust.cargo.project.model.impl.CargoExternalSystemProjectAware.subscribe.first.balloon&quot;: &quot;&quot;,
    &quot;org.rust.first.attach.projects&quot;: &quot;true&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Cargo.Run adaptive_pathfinding">
    <configuration name="Run adaptive_pathfinding" type="CargoCommandRunConfiguration" factoryName="Cargo Command">
      <option name="buildProfileId" value="dev" />
      <option name="command" value="run --package adaptive_pathfinding --bin adaptive_pathfinding" />
      <option name="workingDirectory" value="file://$PROJECT_DIR$" />
      <envs />
      <option name="emulateTerminal" value="true" />
      <option name="channel" value="DEFAULT" />
      <option name="requiredFeatures" value="true" />
      <option name="allFeatures" value="false" />
      <option name="withSudo" value="false" />
      <option name="buildTarget" value="REMOTE" />
      <option name="backtrace" value="SHORT" />
      <option name="isRedirectInput" value="false" />
      <option name="redirectInputPath" value="" />
      <method v="2">
        <option name="CARGO.BUILD_TASK_PROVIDER" enabled="true" />
      </method>
    </configuration>
    <configuration name="Test adaptive_pathfinding" type="CargoCommandRunConfiguration" factoryName="Cargo Command">
      <option name="command" value="test --workspace" />
      <option name="workingDirectory" value="file://$PROJECT_DIR$" />
      <envs />
      <option name="emulateTerminal" value="true" />
      <option name="channel" value="DEFAULT" />
      <option name="requiredFeatures" value="true" />
      <option name="allFeatures" value="false" />
      <option name="withSudo" value="false" />
      <option name="buildTarget" value="REMOTE" />
      <option name="backtrace" value="SHORT" />
      <option name="isRedirectInput" value="false" />
      <option name="redirectInputPath" value="" />
      <method v="2">
        <option name="CARGO.BUILD_TASK_PROVIDER" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="RustProjectSettings">
    <option name="toolchainHomeDirectory" value="$USER_HOME$/.cargo/bin" />
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="854d443b-fe88-47be-9bbe-9b02ac349026" name="更改" comment="" />
      <created>1752644914408</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752644914408</updated>
      <workItem from="1752644915490" duration="9146000" />
      <workItem from="1752712622926" duration="24351000" />
      <workItem from="1753064886451" duration="161000" />
      <workItem from="1753066206745" duration="13131000" />
      <workItem from="1753170602419" duration="15739000" />
      <workItem from="1753255971916" duration="99000" />
      <workItem from="1753256190738" duration="1452000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>