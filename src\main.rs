use image::{<PERSON><PERSON><PERSON><PERSON>, Rgb, RgbImage};
use imageproc::drawing::{draw_hollow_rect_mut, draw_filled_rect_mut, draw_line_segment_mut};
use imageproc::rect::Rect;
use std::collections::{HashMap, BinaryHeap, HashSet};
use std::cmp::Ordering;
use std::time::Instant;
use serde::{Serialize, Deserialize};

/// 节点类型枚举
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Serialize, Deserialize)]
pub enum NodeType {
    Empty,    // 完全空旷
    Blocked,  // 完全被障碍物占据
    Mixed,    // 包含障碍物和空旷区域
}

/// 2D点结构
#[derive(Debug, <PERSON><PERSON>, Co<PERSON>, Serialize, Deserialize)]
pub struct Point {
    pub x: f32,
    pub y: f32,
}

impl Point {
    pub fn new(x: f32, y: f32) -> Self {
        Self { x, y }
    }

    pub fn distance_to(&self, other: &Point) -> f32 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        (dx * dx + dy * dy).sqrt()
    }
}

/// 3D点结构
#[derive(Debu<PERSON>, <PERSON><PERSON>, Copy, Serialize, Deserialize)]
pub struct Point3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

impl Point3D {
    pub fn new(x: f32, y: f32, z: f32) -> Self {
        Self { x, y, z }
    }

    pub fn distance_to(&self, other: &Point3D) -> f32 {
        let dx = self.x - other.x;
        let dy = self.y - other.y;
        let dz = self.z - other.z;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }
}

/// 2D矩形边界
#[derive(Debug, Clone, Copy)]
pub struct Rectangle {
    pub x: f32,
    pub y: f32,
    pub width: f32,
    pub height: f32,
}

/// 3D长方体边界
#[derive(Debug, Clone, Copy, Serialize, Deserialize)]
pub struct Box3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub width: f32,
    pub height: f32,
    pub depth: f32,
}

/// 2D障碍物类型枚举
#[derive(Debug, Clone)]
pub enum Obstacle {
    Rectangle(Rectangle),
    Circle { center: Point, radius: f32 },
    Polygon { vertices: Vec<Point> },
    PointSet { points: Vec<Point>, radius: f32 }, // 点集，每个点有一个影响半径
}

/// 3D障碍物类型枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Obstacle3D {
    Box(Box3D),
    Cylinder { center: Point3D, radius: f32, height: f32 },
    Prism { vertices: Vec<Point>, bottom: f32, height: f32 }, // 棱柱：底面多边形 + 高度
    PointSet { points: Vec<Point3D>, radius: f32 }, // 3D点集，每个点有一个影响半径
}

impl Rectangle {
    pub fn new(x: f32, y: f32, width: f32, height: f32) -> Self {
        Self { x, y, width, height }
    }

    pub fn contains_point(&self, px: f32, py: f32) -> bool {
        px >= self.x && px < self.x + self.width &&
            py >= self.y && py < self.y + self.height
    }

    pub fn center(&self) -> (f32, f32) {
        (self.x + self.width / 2.0, self.y + self.height / 2.0)
    }
}

impl Box3D {
    pub fn new(x: f32, y: f32, z: f32, width: f32, height: f32, depth: f32) -> Self {
        Self { x, y, z, width, height, depth }
    }

    pub fn contains_point(&self, px: f32, py: f32, pz: f32) -> bool {
        px >= self.x && px < self.x + self.width &&
            py >= self.y && py < self.y + self.height &&
            pz >= self.z && pz < self.z + self.depth
    }

    pub fn center(&self) -> (f32, f32, f32) {
        (
            self.x + self.width / 2.0,
            self.y + self.height / 2.0,
            self.z + self.depth / 2.0
        )
    }

    pub fn center_point(&self) -> Point3D {
        let (x, y, z) = self.center();
        Point3D::new(x, y, z)
    }

    pub fn volume(&self) -> f32 {
        self.width * self.height * self.depth
    }
}

impl Obstacle3D {
    /// 检查3D点是否在障碍物内
    pub fn contains_point(&self, x: f32, y: f32, z: f32) -> bool {
        match self {
            Obstacle3D::Box(box3d) => box3d.contains_point(x, y, z),
            Obstacle3D::Cylinder { center, radius, height } => {
                // 检查高度范围
                if z < center.z || z > center.z + height {
                    return false;
                }
                // 检查2D圆形范围
                let dx = x - center.x;
                let dy = y - center.y;
                dx * dx + dy * dy <= radius * radius
            }
            Obstacle3D::Prism { vertices, bottom, height } => {
                // 检查高度范围
                if z < *bottom || z > bottom + height {
                    return false;
                }
                // 使用2D多边形检测
                Self::point_in_polygon(x, y, vertices)
            }
            Obstacle3D::PointSet { points, radius } => {
                points.iter().any(|point| {
                    let dx = x - point.x;
                    let dy = y - point.y;
                    let dz = z - point.z;
                    dx * dx + dy * dy + dz * dz <= radius * radius
                })
            }
        }
    }

    /// 检查障碍物是否与3D长方体区域有重叠
    pub fn overlaps_with_box(&self, box3d: &Box3D) -> bool {
        match self {
            Obstacle3D::Box(obs_box) => {
                Self::box_overlaps(box3d, obs_box)
            }
            Obstacle3D::Cylinder { center, radius, height } => {
                Self::cylinder_overlaps_box(center, *radius, *height, box3d)
            }
            Obstacle3D::Prism { vertices, bottom, height } => {
                Self::prism_overlaps_box(vertices, *bottom, *height, box3d)
            }
            Obstacle3D::PointSet { points, radius } => {
                Self::pointset_overlaps_box(points, *radius, box3d)
            }
        }
    }

    /// 计算障碍物与3D长方体区域的重叠体积
    pub fn overlap_with_box(&self, box3d: &Box3D) -> f32 {
        match self {
            Obstacle3D::Box(obs_box) => {
                Self::box_overlap_volume(box3d, obs_box)
            }
            Obstacle3D::Cylinder { center, radius, height } => {
                Self::cylinder_box_overlap_volume(center, *radius, *height, box3d)
            }
            Obstacle3D::Prism { vertices, bottom, height } => {
                Self::prism_box_overlap_volume(vertices, *bottom, *height, box3d)
            }
            Obstacle3D::PointSet { points, radius } => {
                Self::pointset_box_overlap_volume(points, *radius, box3d)
            }
        }
    }

    /// 检查两个3D长方体是否重叠
    fn box_overlaps(box1: &Box3D, box2: &Box3D) -> bool {
        !(box1.x + box1.width <= box2.x ||
            box2.x + box2.width <= box1.x ||
            box1.y + box1.height <= box2.y ||
            box2.y + box2.height <= box1.y ||
            box1.z + box1.depth <= box2.z ||
            box2.z + box2.depth <= box1.z)
    }

    /// 检查圆柱体是否与3D长方体重叠
    fn cylinder_overlaps_box(center: &Point3D, radius: f32, height: f32, box3d: &Box3D) -> bool {
        // 检查高度范围是否重叠
        if center.z + height <= box3d.z || box3d.z + box3d.depth <= center.z {
            return false;
        }

        // 检查2D圆形与矩形是否重叠
        let closest_x = center.x.max(box3d.x).min(box3d.x + box3d.width);
        let closest_y = center.y.max(box3d.y).min(box3d.y + box3d.height);

        let dx = center.x - closest_x;
        let dy = center.y - closest_y;
        let distance_squared = dx * dx + dy * dy;

        distance_squared <= radius * radius
    }

    /// 检查棱柱是否与3D长方体重叠
    fn prism_overlaps_box(vertices: &[Point], bottom: f32, height: f32, box3d: &Box3D) -> bool {
        // 检查高度范围是否重叠
        if bottom + height <= box3d.z || box3d.z + box3d.depth <= bottom {
            return false;
        }

        // 创建2D矩形用于检测
        let rect = Rectangle::new(box3d.x, box3d.y, box3d.width, box3d.height);

        // 复用2D多边形与矩形重叠检测
        Obstacle::polygon_overlaps_rectangle(vertices, &rect)
    }

    /// 检查3D点集是否与长方体重叠
    fn pointset_overlaps_box(points: &[Point3D], radius: f32, box3d: &Box3D) -> bool {
        for point in points {
            if Self::sphere_overlaps_box(point, radius, box3d) {
                return true;
            }
        }
        false
    }

    /// 检查球体是否与长方体重叠
    fn sphere_overlaps_box(center: &Point3D, radius: f32, box3d: &Box3D) -> bool {
        let closest_x = center.x.max(box3d.x).min(box3d.x + box3d.width);
        let closest_y = center.y.max(box3d.y).min(box3d.y + box3d.height);
        let closest_z = center.z.max(box3d.z).min(box3d.z + box3d.depth);

        let dx = center.x - closest_x;
        let dy = center.y - closest_y;
        let dz = center.z - closest_z;
        let distance_squared = dx * dx + dy * dy + dz * dz;

        distance_squared <= radius * radius
    }

    /// 复用2D的点在多边形内检测
    fn point_in_polygon(x: f32, y: f32, vertices: &[Point]) -> bool {
        Obstacle::point_in_polygon(x, y, vertices)
    }

    /// 3D长方体重叠体积计算
    fn box_overlap_volume(box1: &Box3D, box2: &Box3D) -> f32 {
        let x1 = box1.x.max(box2.x);
        let y1 = box1.y.max(box2.y);
        let z1 = box1.z.max(box2.z);
        let x2 = (box1.x + box1.width).min(box2.x + box2.width);
        let y2 = (box1.y + box1.height).min(box2.y + box2.height);
        let z2 = (box1.z + box1.depth).min(box2.z + box2.depth);

        if x2 > x1 && y2 > y1 && z2 > z1 {
            (x2 - x1) * (y2 - y1) * (z2 - z1)
        } else {
            0.0
        }
    }

    /// 圆柱体与长方体重叠体积（近似计算）
    fn cylinder_box_overlap_volume(center: &Point3D, radius: f32, height: f32, box3d: &Box3D) -> f32 {
        // 简化计算：采样检测
        let samples = 10;
        let step_x = box3d.width / samples as f32;
        let step_y = box3d.height / samples as f32;
        let step_z = box3d.depth / samples as f32;
        let sample_volume = step_x * step_y * step_z;

        let mut overlap_samples = 0;
        for i in 0..samples {
            for j in 0..samples {
                for k in 0..samples {
                    let x = box3d.x + (i as f32 + 0.5) * step_x;
                    let y = box3d.y + (j as f32 + 0.5) * step_y;
                    let z = box3d.z + (k as f32 + 0.5) * step_z;

                    // 检查是否在圆柱体内
                    if z >= center.z && z <= center.z + height {
                        let dx = x - center.x;
                        let dy = y - center.y;
                        if dx * dx + dy * dy <= radius * radius {
                            overlap_samples += 1;
                        }
                    }
                }
            }
        }

        overlap_samples as f32 * sample_volume
    }

    /// 棱柱与长方体重叠体积（近似计算）
    fn prism_box_overlap_volume(vertices: &[Point], bottom: f32, height: f32, box3d: &Box3D) -> f32 {
        // 简化计算：采样检测
        let samples = 10;
        let step_x = box3d.width / samples as f32;
        let step_y = box3d.height / samples as f32;
        let step_z = box3d.depth / samples as f32;
        let sample_volume = step_x * step_y * step_z;

        let mut overlap_samples = 0;
        for i in 0..samples {
            for j in 0..samples {
                for k in 0..samples {
                    let x = box3d.x + (i as f32 + 0.5) * step_x;
                    let y = box3d.y + (j as f32 + 0.5) * step_y;
                    let z = box3d.z + (k as f32 + 0.5) * step_z;

                    // 检查是否在棱柱内
                    if z >= bottom && z <= bottom + height {
                        if Self::point_in_polygon(x, y, vertices) {
                            overlap_samples += 1;
                        }
                    }
                }
            }
        }

        overlap_samples as f32 * sample_volume
    }

    /// 3D点集与长方体重叠体积
    fn pointset_box_overlap_volume(points: &[Point3D], radius: f32, box3d: &Box3D) -> f32 {
        let mut total_overlap = 0.0;
        for point in points {
            total_overlap += Self::sphere_box_overlap_volume(point, radius, box3d);
        }
        total_overlap
    }

    /// 球体与长方体重叠体积（近似计算）
    fn sphere_box_overlap_volume(center: &Point3D, radius: f32, box3d: &Box3D) -> f32 {
        // 简化计算：采样检测
        let samples = 10;
        let step_x = box3d.width / samples as f32;
        let step_y = box3d.height / samples as f32;
        let step_z = box3d.depth / samples as f32;
        let sample_volume = step_x * step_y * step_z;

        let mut overlap_samples = 0;
        for i in 0..samples {
            for j in 0..samples {
                for k in 0..samples {
                    let x = box3d.x + (i as f32 + 0.5) * step_x;
                    let y = box3d.y + (j as f32 + 0.5) * step_y;
                    let z = box3d.z + (k as f32 + 0.5) * step_z;

                    let dx = x - center.x;
                    let dy = y - center.y;
                    let dz = z - center.z;
                    if dx * dx + dy * dy + dz * dz <= radius * radius {
                        overlap_samples += 1;
                    }
                }
            }
        }

        overlap_samples as f32 * sample_volume
    }
}

impl Obstacle {
    /// 检查点是否在障碍物内
    pub fn contains_point(&self, x: f32, y: f32) -> bool {
        match self {
            Obstacle::Rectangle(rect) => rect.contains_point(x, y),
            Obstacle::Circle { center, radius } => {
                let dx = x - center.x;
                let dy = y - center.y;
                dx * dx + dy * dy <= radius * radius
            }
            Obstacle::Polygon { vertices } => {
                Self::point_in_polygon(x, y, vertices)
            }
            Obstacle::PointSet { points, radius } => {
                points.iter().any(|point| {
                    let dx = x - point.x;
                    let dy = y - point.y;
                    dx * dx + dy * dy <= radius * radius
                })
            }
        }
    }

    /// 检查障碍物是否与矩形区域有重叠（用于网格分类）
    /// 对于路径规划，只要有任何重叠就认为整个网格不可通行
    pub fn overlaps_with_rectangle(&self, rect: &Rectangle) -> bool {
        match self {
            Obstacle::Rectangle(obs_rect) => {
                Self::rectangle_overlaps(rect, obs_rect)
            }
            Obstacle::Circle { center, radius } => {
                Self::circle_overlaps_rectangle(center, *radius, rect)
            }
            Obstacle::Polygon { vertices } => {
                Self::polygon_overlaps_rectangle(vertices, rect)
            }
            Obstacle::PointSet { points, radius } => {
                Self::pointset_overlaps_rectangle(points, *radius, rect)
            }
        }
    }

    /// 计算障碍物与矩形区域的重叠面积（用于可视化和统计）
    pub fn overlap_with_rectangle(&self, rect: &Rectangle) -> f32 {
        match self {
            Obstacle::Rectangle(obs_rect) => {
                Self::rectangle_overlap(rect, obs_rect)
            }
            Obstacle::Circle { center, radius } => {
                Self::circle_rectangle_overlap(center, *radius, rect)
            }
            Obstacle::Polygon { vertices } => {
                Self::polygon_rectangle_overlap(vertices, rect)
            }
            Obstacle::PointSet { points, radius } => {
                Self::pointset_rectangle_overlap(points, *radius, rect)
            }
        }
    }

    /// 检查两个矩形是否重叠
    fn rectangle_overlaps(rect1: &Rectangle, rect2: &Rectangle) -> bool {
        !(rect1.x + rect1.width <= rect2.x ||
            rect2.x + rect2.width <= rect1.x ||
            rect1.y + rect1.height <= rect2.y ||
            rect2.y + rect2.height <= rect1.y)
    }

    /// 检查圆形是否与矩形重叠
    fn circle_overlaps_rectangle(center: &Point, radius: f32, rect: &Rectangle) -> bool {
        // 找到矩形上距离圆心最近的点
        let closest_x = center.x.max(rect.x).min(rect.x + rect.width);
        let closest_y = center.y.max(rect.y).min(rect.y + rect.height);

        // 计算距离
        let dx = center.x - closest_x;
        let dy = center.y - closest_y;
        let distance_squared = dx * dx + dy * dy;

        distance_squared <= radius * radius
    }

    /// 检查多边形是否与矩形重叠
    fn polygon_overlaps_rectangle(vertices: &[Point], rect: &Rectangle) -> bool {
        if vertices.len() < 3 {
            return false;
        }

        // 检查多边形顶点是否在矩形内
        for vertex in vertices {
            if rect.contains_point(vertex.x, vertex.y) {
                return true;
            }
        }

        // 检查矩形顶点是否在多边形内
        let rect_corners = [
            (rect.x, rect.y),
            (rect.x + rect.width, rect.y),
            (rect.x + rect.width, rect.y + rect.height),
            (rect.x, rect.y + rect.height),
        ];

        for &(x, y) in &rect_corners {
            if Self::point_in_polygon(x, y, vertices) {
                return true;
            }
        }

        // 检查多边形边是否与矩形边相交
        for i in 0..vertices.len() {
            let j = (i + 1) % vertices.len();
            let line_start = &vertices[i];
            let line_end = &vertices[j];

            // 检查与矩形四条边的相交
            if Self::line_intersects_rectangle(line_start, line_end, rect) {
                return true;
            }
        }

        false
    }

    /// 检查点集是否与矩形重叠
    fn pointset_overlaps_rectangle(points: &[Point], radius: f32, rect: &Rectangle) -> bool {
        for point in points {
            if Self::circle_overlaps_rectangle(point, radius, rect) {
                return true;
            }
        }
        false
    }

    /// 检查线段是否与矩形相交
    fn line_intersects_rectangle(line_start: &Point, line_end: &Point, rect: &Rectangle) -> bool {
        // 简化版本：检查线段是否与矩形的任一边相交
        let rect_lines = [
            (Point::new(rect.x, rect.y), Point::new(rect.x + rect.width, rect.y)),
            (Point::new(rect.x + rect.width, rect.y), Point::new(rect.x + rect.width, rect.y + rect.height)),
            (Point::new(rect.x + rect.width, rect.y + rect.height), Point::new(rect.x, rect.y + rect.height)),
            (Point::new(rect.x, rect.y + rect.height), Point::new(rect.x, rect.y)),
        ];

        for (rect_start, rect_end) in &rect_lines {
            if Self::lines_intersect(line_start, line_end, rect_start, rect_end) {
                return true;
            }
        }

        false
    }

    /// 检查两条线段是否相交
    fn lines_intersect(p1: &Point, q1: &Point, p2: &Point, q2: &Point) -> bool {
        fn orientation(p: &Point, q: &Point, r: &Point) -> i32 {
            let val = (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);
            if val.abs() < 1e-10 { 0 } // 共线
            else if val > 0.0 { 1 } // 顺时针
            else { 2 } // 逆时针
        }

        fn on_segment(p: &Point, q: &Point, r: &Point) -> bool {
            q.x <= p.x.max(r.x) && q.x >= p.x.min(r.x) &&
                q.y <= p.y.max(r.y) && q.y >= p.y.min(r.y)
        }

        let o1 = orientation(p1, q1, p2);
        let o2 = orientation(p1, q1, q2);
        let o3 = orientation(p2, q2, p1);
        let o4 = orientation(p2, q2, q1);

        // 一般情况
        if o1 != o2 && o3 != o4 {
            return true;
        }

        // 特殊情况：共线且重叠
        (o1 == 0 && on_segment(p1, p2, q1)) ||
            (o2 == 0 && on_segment(p1, q2, q1)) ||
            (o3 == 0 && on_segment(p2, p1, q2)) ||
            (o4 == 0 && on_segment(p2, q1, q2))
    }

    /// 矩形与矩形重叠面积（保留用于统计）
    fn rectangle_overlap(rect1: &Rectangle, rect2: &Rectangle) -> f32 {
        let x1 = rect1.x.max(rect2.x);
        let y1 = rect1.y.max(rect2.y);
        let x2 = (rect1.x + rect1.width).min(rect2.x + rect2.width);
        let y2 = (rect1.y + rect1.height).min(rect2.y + rect2.height);

        if x2 > x1 && y2 > y1 {
            (x2 - x1) * (y2 - y1)
        } else {
            0.0
        }
    }

    /// 圆形与矩形重叠面积（近似计算）
    fn circle_rectangle_overlap(center: &Point, radius: f32, rect: &Rectangle) -> f32 {
        // 简化计算：采样检测
        let samples = 20;
        let step_x = rect.width / samples as f32;
        let step_y = rect.height / samples as f32;
        let sample_area = step_x * step_y;

        let mut overlap_samples = 0;
        for i in 0..samples {
            for j in 0..samples {
                let x = rect.x + (i as f32 + 0.5) * step_x;
                let y = rect.y + (j as f32 + 0.5) * step_y;

                let dx = x - center.x;
                let dy = y - center.y;
                if dx * dx + dy * dy <= radius * radius {
                    overlap_samples += 1;
                }
            }
        }

        overlap_samples as f32 * sample_area
    }

    /// 多边形与矩形重叠面积（近似计算）
    fn polygon_rectangle_overlap(vertices: &[Point], rect: &Rectangle) -> f32 {
        // 简化计算：采样检测
        let samples = 20;
        let step_x = rect.width / samples as f32;
        let step_y = rect.height / samples as f32;
        let sample_area = step_x * step_y;

        let mut overlap_samples = 0;
        for i in 0..samples {
            for j in 0..samples {
                let x = rect.x + (i as f32 + 0.5) * step_x;
                let y = rect.y + (j as f32 + 0.5) * step_y;

                if Self::point_in_polygon(x, y, vertices) {
                    overlap_samples += 1;
                }
            }
        }

        overlap_samples as f32 * sample_area
    }

    /// 点集与矩形重叠面积
    fn pointset_rectangle_overlap(points: &[Point], radius: f32, rect: &Rectangle) -> f32 {
        let mut total_overlap = 0.0;
        for point in points {
            total_overlap += Self::circle_rectangle_overlap(point, radius, rect);
        }
        total_overlap
    }

    /// 点在多边形内检测（射线法）
    fn point_in_polygon(x: f32, y: f32, vertices: &[Point]) -> bool {
        if vertices.len() < 3 {
            return false;
        }

        let mut inside = false;
        let mut j = vertices.len() - 1;

        for i in 0..vertices.len() {
            let xi = vertices[i].x;
            let yi = vertices[i].y;
            let xj = vertices[j].x;
            let yj = vertices[j].y;

            if ((yi > y) != (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi) {
                inside = !inside;
            }
            j = i;
        }

        inside
    }
}

/// 四叉树节点
#[derive(Debug)]
pub struct QuadTreeNode {
    pub bounds: Rectangle,
    pub level: u8,
    pub node_type: NodeType,
    pub children: Option<Box<[QuadTreeNode; 4]>>,
}

/// 八叉树节点
#[derive(Debug)]
pub struct OctreeNode {
    pub bounds: Box3D,
    pub level: u8,
    pub node_type: NodeType,
    pub children: Option<Box<[OctreeNode; 8]>>,
}

impl QuadTreeNode {
    pub fn new(bounds: Rectangle, level: u8) -> Self {
        Self {
            bounds,
            level,
            node_type: NodeType::Empty,
            children: None,
        }
    }

    pub fn is_leaf(&self) -> bool {
        self.children.is_none()
    }

    /// 获取所有叶子节点
    pub fn get_leaf_nodes(&self) -> Vec<&QuadTreeNode> {
        if self.is_leaf() {
            vec![self]
        } else {
            let mut leaves = Vec::new();
            if let Some(children) = &self.children {
                for child in children.iter() {
                    leaves.extend(child.get_leaf_nodes());
                }
            }
            leaves
        }
    }
}

impl OctreeNode {
    pub fn new(bounds: Box3D, level: u8) -> Self {
        Self {
            bounds,
            level,
            node_type: NodeType::Empty,
            children: None,
        }
    }

    pub fn is_leaf(&self) -> bool {
        self.children.is_none()
    }

    /// 获取所有叶子节点
    pub fn get_leaf_nodes(&self) -> Vec<&OctreeNode> {
        if self.is_leaf() {
            vec![self]
        } else {
            let mut leaves = Vec::new();
            if let Some(children) = &self.children {
                for child in children.iter() {
                    leaves.extend(child.get_leaf_nodes());
                }
            }
            leaves
        }
    }
}

/// 2D网格单元
#[derive(Debug, Clone)]
pub struct GridCell {
    pub id: usize,
    pub bounds: Rectangle,
    pub level: u8,
    pub node_type: NodeType,
}

/// 3D网格单元
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridCell3D {
    pub id: usize,
    pub bounds: Box3D,
    pub level: u8,
    pub node_type: NodeType,
}

/// 3D网格边界信息（用于可视化）
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GridBoundary3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
    pub width: f32,
    pub height: f32,
    pub depth: f32,
    pub level: u8,
    pub node_type: NodeType,
}

/// 管道门 - 连接相邻网格的通道
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CorridorGate {
    pub id: usize,
    pub cell_a: usize,  // 第一个网格ID
    pub cell_b: usize,  // 第二个网格ID
    pub gate_type: GateType,
    pub shared_face: SharedFace3D,  // 共享面的几何信息
    pub effective_area: f32,  // 有效通行面积
    pub center_point: Point3D,  // 门的中心点
    pub normal_vector: Vector3D,  // 门的法向量
}

/// 门的类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum GateType {
    SameLevel,      // 同级网格之间
    CrossLevel,     // 跨级网格之间
}

/// 共享面的几何信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SharedFace3D {
    pub vertices: Vec<Point3D>,  // 共享面的顶点
    pub area: f32,               // 面积
    pub bounds: Box3D,           // 包围盒
    pub face_direction: FaceDirection,  // 面的方向
}

/// 面的方向
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum FaceDirection {
    XPositive,  // X轴正方向面
    XNegative,  // X轴负方向面
    YPositive,  // Y轴正方向面
    YNegative,  // Y轴负方向面
    ZPositive,  // Z轴正方向面
    ZNegative,  // Z轴负方向面
}

/// 3D向量
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Vector3D {
    pub x: f32,
    pub y: f32,
    pub z: f32,
}

/// 管道信息 - 由一系列门组成的通道
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Corridor {
    pub id: usize,
    pub gates: Vec<usize>,  // 门的ID序列
    pub cells: Vec<usize>,  // 经过的网格单元ID序列
    pub total_length: f32,  // 管道总长度
    pub min_cross_section: f32,  // 最小截面积
    pub start_point: Point3D,  // 管道起点
    pub end_point: Point3D,    // 管道终点
}

impl CorridorGate {
    /// 计算通过此门的代价
    pub fn calculate_traversal_cost(&self) -> f32 {
        // 基础代价使用门的有效面积的倒数作为基础
        let base_cost = 1.0 / (self.effective_area + 0.1); // 避免除零

        // 跨级网格的代价稍高
        let level_penalty = match self.gate_type {
            GateType::SameLevel => 1.0,
            GateType::CrossLevel => 1.2,
        };

        // 面积越小，代价越高（但目前假设所有门都足够大）
        let area_factor = 1.0; // 暂时不考虑面积影响

        base_cost * level_penalty * area_factor
    }
}

impl SharedFace3D {
    /// 获取共享面的中心点
    pub fn center_point(&self) -> Point3D {
        self.bounds.center_point()
    }

    /// 获取共享面的法向量
    pub fn normal_vector(&self) -> Vector3D {
        match self.face_direction {
            FaceDirection::XPositive => Vector3D { x: 1.0, y: 0.0, z: 0.0 },
            FaceDirection::XNegative => Vector3D { x: -1.0, y: 0.0, z: 0.0 },
            FaceDirection::YPositive => Vector3D { x: 0.0, y: 1.0, z: 0.0 },
            FaceDirection::YNegative => Vector3D { x: 0.0, y: -1.0, z: 0.0 },
            FaceDirection::ZPositive => Vector3D { x: 0.0, y: 0.0, z: 1.0 },
            FaceDirection::ZNegative => Vector3D { x: 0.0, y: 0.0, z: -1.0 },
        }
    }
}

/// 漏斗算法的门户
#[derive(Debug, Clone)]
pub struct FunnelGate {
    pub left: Point3D,    // 左边界点
    pub right: Point3D,   // 右边界点
    pub center: Point3D,  // 中心点
}

/// 最远可达目标类型
#[derive(Debug, Clone)]
enum FurthestTarget {
    EndPoint,           // 可以直接到达终点
    Gate(usize),        // 需要通过指定索引的门户
}

/// 漏斗算法的细化路径
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RefinedPath {
    pub points: Vec<Point3D>,  // 细化后的路径点
    pub total_length: f32,     // 路径总长度
    pub smoothness: f32,       // 平滑度指标
}

/// 虚拟细网格中的节点
#[derive(Debug, Clone, PartialEq, Eq, Hash)]
pub struct VirtualGridNode {
    pub x: i32,
    pub y: i32,
    pub z: i32,
}

impl VirtualGridNode {
    pub fn new(x: i32, y: i32, z: i32) -> Self {
        Self { x, y, z }
    }

    /// 获取26邻域坐标
    pub fn get_neighbors_26(&self) -> Vec<VirtualGridNode> {
        let mut neighbors = Vec::new();
        for dx in -1..=1 {
            for dy in -1..=1 {
                for dz in -1..=1 {
                    if dx == 0 && dy == 0 && dz == 0 {
                        continue; // 跳过自己
                    }
                    neighbors.push(VirtualGridNode::new(
                        self.x + dx,
                        self.y + dy,
                        self.z + dz,
                    ));
                }
            }
        }
        neighbors
    }

    /// 获取6邻域坐标（只考虑面相邻）
    pub fn get_neighbors_6(&self) -> Vec<VirtualGridNode> {
        vec![
            VirtualGridNode::new(self.x + 1, self.y, self.z),
            VirtualGridNode::new(self.x - 1, self.y, self.z),
            VirtualGridNode::new(self.x, self.y + 1, self.z),
            VirtualGridNode::new(self.x, self.y - 1, self.z),
            VirtualGridNode::new(self.x, self.y, self.z + 1),
            VirtualGridNode::new(self.x, self.y, self.z - 1),
        ]
    }

    /// 转换为Point3D（假设细网格大小为1.0）
    pub fn to_point3d(&self) -> Point3D {
        Point3D::new(self.x as f32 + 0.5, self.y as f32 + 0.5, self.z as f32 + 0.5)
    }

    /// 计算到另一个节点的距离
    pub fn distance_to(&self, other: &VirtualGridNode) -> f32 {
        let dx = (self.x - other.x) as f32;
        let dy = (self.y - other.y) as f32;
        let dz = (self.z - other.z) as f32;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }
}

/// 虚拟细网格路径节点
#[derive(Debug, Clone)]
pub struct VirtualPathNode {
    pub node: VirtualGridNode,
    pub g_cost: f32,
    pub h_cost: f32,
    pub parent: Option<VirtualGridNode>,
}

impl VirtualPathNode {
    pub fn f_cost(&self) -> f32 {
        self.g_cost + self.h_cost
    }
}

impl Eq for VirtualPathNode {}

impl PartialEq for VirtualPathNode {
    fn eq(&self, other: &Self) -> bool {
        self.node == other.node
    }
}

impl Ord for VirtualPathNode {
    fn cmp(&self, other: &Self) -> Ordering {
        // 注意：BinaryHeap是最大堆，我们需要最小堆，所以反转比较
        other.f_cost().partial_cmp(&self.f_cost()).unwrap_or(Ordering::Equal)
    }
}

impl PartialOrd for VirtualPathNode {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

/// 3D体素数据（用于Python可视化）
#[derive(Debug, Serialize, Deserialize)]
pub struct VoxelData3D {
    pub size_xy: u32,  // XY平面边长
    pub size_z: u32,   // Z轴高度
    pub voxels: Vec<u8>,  // 0=空, 1=空旷网格, 2=被占据网格, 3=障碍物
    pub grid_boundaries: Vec<GridBoundary3D>,
    pub path_points: Option<Vec<Point3D>>,  // 原始A*路径点
    pub corridor_gates: Option<Vec<CorridorGate>>,  // 管道门
    pub corridors: Option<Vec<Corridor>>,  // 管道信息
    pub refined_path: Option<RefinedPath>,  // 漏斗算法细化后的路径
}

/// 路径规划中的节点
#[derive(Debug, Clone)]
pub struct PathNode {
    pub cell_id: usize,
    pub g_cost: f32,
    pub h_cost: f32,
    pub parent: Option<usize>,
}

impl PathNode {
    pub fn f_cost(&self) -> f32 {
        self.g_cost + self.h_cost
    }
}

impl Eq for PathNode {}

impl PartialEq for PathNode {
    fn eq(&self, other: &Self) -> bool {
        self.cell_id == other.cell_id
    }
}

impl Ord for PathNode {
    fn cmp(&self, other: &Self) -> Ordering {
        // 注意：BinaryHeap是最大堆，我们需要最小堆，所以反转比较
        other.f_cost().partial_cmp(&self.f_cost()).unwrap_or(Ordering::Equal)
    }
}

impl PartialOrd for PathNode {
    fn partial_cmp(&self, other: &Self) -> Option<Ordering> {
        Some(self.cmp(other))
    }
}

/// 2D多分辨率网格系统 - 基于规则网格而非四叉树
pub struct MultiResolutionGrid {
    pub cells: Vec<GridCell>,
    pub map_size: u32,  // 改为整数，确保对齐
    pub obstacles: Vec<Obstacle>,
    pub adjacency: HashMap<usize, Vec<(usize, f32)>>, // 邻接关系：(邻居ID, 边代价)
}

/// 3D多分辨率网格系统 - 基于八叉树规则网格
pub struct MultiResolutionGrid3D {
    pub cells: Vec<GridCell3D>,
    pub map_size: u32,  // XY平面边长，确保是2的幂次
    pub map_height: u32, // Z轴高度
    pub obstacles: Vec<Obstacle3D>,
    pub adjacency: HashMap<usize, Vec<(usize, f32)>>, // 邻接关系：(邻居ID, 边代价)
    pub corridor_gates: Vec<CorridorGate>,  // 管道门
    pub corridors: Vec<Corridor>,  // 管道信息
}

impl MultiResolutionGrid3D {
    pub fn new(map_size: u32, map_height: u32) -> Self {
        Self {
            cells: Vec::new(),
            map_size,
            map_height,
            obstacles: Vec::new(),
            adjacency: HashMap::new(),
            corridor_gates: Vec::new(),
            corridors: Vec::new(),
        }
    }

    /// 添加3D障碍物
    pub fn add_obstacle(&mut self, obstacle: Obstacle3D) {
        self.obstacles.push(obstacle);
    }

    /// 检查3D区域是否包含障碍物
    fn check_obstacle_in_region(region: &Box3D, obstacles: &[Obstacle3D]) -> NodeType {
        // 对于1x1x1的最小网格，采用保守策略：只要有重叠就不可通行
        if region.width <= 1.0 && region.height <= 1.0 && region.depth <= 1.0 {
            for obstacle in obstacles {
                if obstacle.overlaps_with_box(region) {
                    return NodeType::Blocked;
                }
            }
            return NodeType::Empty;
        }

        // 对于较大的网格，检查重叠情况来决定是否需要细分
        let mut has_overlap = false;
        let region_volume = region.width * region.height * region.depth;
        let mut total_overlap_volume = 0.0;

        for obstacle in obstacles {
            if obstacle.overlaps_with_box(region) {
                has_overlap = true;
                let overlap_volume = obstacle.overlap_with_box(region);
                total_overlap_volume += overlap_volume;
            }
        }

        if !has_overlap {
            NodeType::Empty
        } else if total_overlap_volume >= region_volume * 0.99 {
            NodeType::Blocked  // 几乎完全被覆盖
        } else {
            NodeType::Mixed    // 部分覆盖，需要细分
        }
    }

    /// 构建3D自适应网格 - 使用分层规则网格
    pub fn build_adaptive_grid(&mut self) {
        self.cells.clear();
        self.adjacency.clear();
        self.corridor_gates.clear();
        self.corridors.clear();

        // 从最粗的网格开始，递归细化需要细化的区域
        // 限制最大深度以提高性能（减少网格单元数量）
        let theoretical_max_level = (self.map_size as f32).log2() as u8;
        let min_grid_size = 2.0; // 硬编码最小网格大小，可以根据需要修改为1.0, 4.0等
        let max_level = std::cmp::min(theoretical_max_level, (self.map_size as f32 / min_grid_size).log2() as u8); // 限制最大深度

        // 从整个3D地图开始递归细化
        let root_bounds = Box3D::new(0.0, 0.0, 0.0,
                                     self.map_size as f32,
                                     self.map_size as f32,
                                     self.map_height as f32);
        self.subdivide_region(root_bounds, 0, max_level);

        // 为每个单元分配ID
        for (i, cell) in self.cells.iter_mut().enumerate() {
            cell.id = i;
        }

        // 构建邻接关系和管道门
        self.build_adjacency_with_gates();
    }

    /// 递归细分3D区域 - 确保网格边界对齐
    fn subdivide_region(&mut self, bounds: Box3D, level: u8, max_level: u8) {
        // 检查当前区域的类型
        let node_type = Self::check_obstacle_in_region(&bounds, &self.obstacles);

        // 如果是纯空旷或纯障碍区域，或者已达到最大深度，或者已经是1x1x1大小，则停止细分
        if node_type != NodeType::Mixed || level >= max_level ||
            bounds.width <= 1.0 || bounds.height <= 1.0 || bounds.depth <= 1.0 {
            let final_node_type = if node_type == NodeType::Mixed {
                NodeType::Blocked
            } else {
                node_type
            };
            let cell = GridCell3D {
                id: 0, // 临时ID，稍后会重新分配
                bounds,
                level,
                node_type: final_node_type,
            };
            self.cells.push(cell);
            return;
        }

        // 如果是混合区域，则细分为8个子区域
        let current_size = bounds.width as u32;
        let half_size = current_size / 2;

        // 只有当当前大小是偶数时才能均匀细分
        if current_size % 2 != 0 || half_size == 0 {
            let cell = GridCell3D {
                id: 0, // 临时ID，稍后会重新分配
                bounds,
                level,
                node_type,
            };
            self.cells.push(cell);
            return;
        }

        let x = bounds.x as u32;
        let y = bounds.y as u32;
        let z = bounds.z as u32;

        // 8个子立方体
        let children = [
            Box3D::new(x as f32, y as f32, z as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new((x + half_size) as f32, y as f32, z as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new(x as f32, (y + half_size) as f32, z as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new((x + half_size) as f32, (y + half_size) as f32, z as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new(x as f32, y as f32, (z + half_size) as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new((x + half_size) as f32, y as f32, (z + half_size) as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new(x as f32, (y + half_size) as f32, (z + half_size) as f32, half_size as f32, half_size as f32, half_size as f32),
            Box3D::new((x + half_size) as f32, (y + half_size) as f32, (z + half_size) as f32, half_size as f32, half_size as f32, half_size as f32),
        ];

        // 递归处理每个子区域
        for child_bounds in children.iter() {
            self.subdivide_region(*child_bounds, level + 1, max_level);
        }
    }

    /// 构建3D邻接关系和管道门 - 优化版本
    fn build_adjacency_with_gates(&mut self) {
        self.adjacency.clear();
        self.corridor_gates.clear();

        println!("构建邻接关系和管道门中... ({} 个网格单元)", self.cells.len());
        let adjacency_start = std::time::Instant::now();

        // 预分配空间
        for i in 0..self.cells.len() {
            self.adjacency.insert(i, Vec::new());
        }

        let mut gate_id = 0;

        // 使用对称性优化：只检查i < j的情况
        for i in 0..self.cells.len() {
            for j in (i+1)..self.cells.len() {
                if let Some(gate) = self.create_gate_if_adjacent(i, j, gate_id) {
                    let cost = gate.calculate_traversal_cost();

                    // 添加双向邻接关系
                    self.adjacency.get_mut(&i).unwrap().push((j, cost));
                    self.adjacency.get_mut(&j).unwrap().push((i, cost));

                    // 保存门信息
                    self.corridor_gates.push(gate);
                    gate_id += 1;
                }
            }

            // 进度显示
            if i % 1000 == 0 && i > 0 {
                println!("  已处理 {}/{} 个网格单元", i, self.cells.len());
            }
        }

        let adjacency_duration = adjacency_start.elapsed();
        println!("邻接关系和管道门构建完成，耗时: {:.2?}", adjacency_duration);
        println!("创建了 {} 个管道门", self.corridor_gates.len());
    }

    /// 构建3D邻接关系 - 优化版本（保留原方法以兼容）
    fn build_adjacency(&mut self) {
        self.adjacency.clear();

        println!("构建邻接关系中... ({} 个网格单元)", self.cells.len());
        let adjacency_start = std::time::Instant::now();

        // 预分配空间
        for i in 0..self.cells.len() {
            self.adjacency.insert(i, Vec::new());
        }

        // 使用对称性优化：只检查i < j的情况
        for i in 0..self.cells.len() {
            for j in (i+1)..self.cells.len() {
                if self.are_adjacent(i, j) {
                    let cost = self.calculate_edge_cost(i, j);

                    // 添加双向邻接关系
                    self.adjacency.get_mut(&i).unwrap().push((j, cost));
                    self.adjacency.get_mut(&j).unwrap().push((i, cost));
                }
            }

            // 进度显示
            if i % 1000 == 0 && i > 0 {
                println!("  已处理 {}/{} 个网格单元", i, self.cells.len());
            }
        }

        let adjacency_duration = adjacency_start.elapsed();
        println!("邻接关系构建完成，耗时: {:.2?}", adjacency_duration);
    }

    /// 检查两个3D网格单元是否相邻
    fn are_adjacent(&self, cell_a_id: usize, cell_b_id: usize) -> bool {
        let cell_a = &self.cells[cell_a_id];
        let cell_b = &self.cells[cell_b_id];

        // 只有空旷单元才能通行
        if cell_a.node_type == NodeType::Blocked || cell_b.node_type == NodeType::Blocked {
            return false;
        }

        // 检查两个3D长方体是否共享面
        let a = &cell_a.bounds;
        let b = &cell_b.bounds;

        // 检查X方向相邻（YZ面共享）
        let x_adjacent = (a.x + a.width == b.x || b.x + b.width == a.x) &&
            !(a.y + a.height <= b.y || b.y + b.height <= a.y) &&
            !(a.z + a.depth <= b.z || b.z + b.depth <= a.z);

        // 检查Y方向相邻（XZ面共享）
        let y_adjacent = (a.y + a.height == b.y || b.y + b.height == a.y) &&
            !(a.x + a.width <= b.x || b.x + b.width <= a.x) &&
            !(a.z + a.depth <= b.z || b.z + b.depth <= a.z);

        // 检查Z方向相邻（XY面共享）
        let z_adjacent = (a.z + a.depth == b.z || b.z + b.depth == a.z) &&
            !(a.x + a.width <= b.x || b.x + b.width <= a.x) &&
            !(a.y + a.height <= b.y || b.y + b.height <= a.y);

        x_adjacent || y_adjacent || z_adjacent
    }

    /// 计算两个相邻3D网格单元之间的边代价
    fn calculate_edge_cost(&self, cell_a_id: usize, cell_b_id: usize) -> f32 {
        let cell_a = &self.cells[cell_a_id];
        let cell_b = &self.cells[cell_b_id];

        let center_a = cell_a.bounds.center();
        let center_b = cell_b.bounds.center();

        // 使用3D欧几里得距离
        let dx = center_a.0 - center_b.0;
        let dy = center_a.1 - center_b.1;
        let dz = center_a.2 - center_b.2;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }

    /// 如果两个网格相邻，创建管道门
    fn create_gate_if_adjacent(&self, cell_a_id: usize, cell_b_id: usize, gate_id: usize) -> Option<CorridorGate> {
        let cell_a = &self.cells[cell_a_id];
        let cell_b = &self.cells[cell_b_id];

        // 只有空旷单元才能通行
        if cell_a.node_type == NodeType::Blocked || cell_b.node_type == NodeType::Blocked {
            return None;
        }

        // 检查是否相邻并计算共享面
        if let Some(shared_face) = self.calculate_shared_face_3d(cell_a, cell_b) {
            let gate_type = if cell_a.level == cell_b.level {
                GateType::SameLevel
            } else {
                GateType::CrossLevel
            };

            let center_point = shared_face.center_point();
            let normal_vector = shared_face.normal_vector();

            Some(CorridorGate {
                id: gate_id,
                cell_a: cell_a_id,
                cell_b: cell_b_id,
                gate_type,
                effective_area: shared_face.area,
                center_point,
                normal_vector,
                shared_face,
            })
        } else {
            None
        }
    }

    /// 计算两个网格单元的共享面
    fn calculate_shared_face_3d(&self, cell_a: &GridCell3D, cell_b: &GridCell3D) -> Option<SharedFace3D> {
        let a = &cell_a.bounds;
        let b = &cell_b.bounds;

        // 检查X方向相邻（YZ面共享）
        if (a.x + a.width == b.x || b.x + b.width == a.x) &&
            !(a.y + a.height <= b.y || b.y + b.height <= a.y) &&
            !(a.z + a.depth <= b.z || b.z + b.depth <= a.z) {
            return self.create_shared_face_x(a, b);
        }

        // 检查Y方向相邻（XZ面共享）
        if (a.y + a.height == b.y || b.y + b.height == a.y) &&
            !(a.x + a.width <= b.x || b.x + b.width <= a.x) &&
            !(a.z + a.depth <= b.z || b.z + b.depth <= a.z) {
            return self.create_shared_face_y(a, b);
        }

        // 检查Z方向相邻（XY面共享）
        if (a.z + a.depth == b.z || b.z + b.depth == a.z) &&
            !(a.x + a.width <= b.x || b.x + b.width <= a.x) &&
            !(a.y + a.height <= b.y || b.y + b.height <= a.y) {
            return self.create_shared_face_z(a, b);
        }

        None
    }

    /// 创建X方向的共享面（YZ面）
    fn create_shared_face_x(&self, a: &Box3D, b: &Box3D) -> Option<SharedFace3D> {
        // 计算重叠区域
        let y_min = a.y.max(b.y);
        let y_max = (a.y + a.height).min(b.y + b.height);
        let z_min = a.z.max(b.z);
        let z_max = (a.z + a.depth).min(b.z + b.depth);

        if y_max <= y_min || z_max <= z_min {
            return None;
        }

        let x_pos = if a.x + a.width == b.x { a.x + a.width } else { b.x + b.width };
        let face_direction = if a.x + a.width == b.x {
            FaceDirection::XPositive
        } else {
            FaceDirection::XNegative
        };

        let width = y_max - y_min;
        let height = z_max - z_min;
        let area = width * height;

        let vertices = vec![
            Point3D::new(x_pos, y_min, z_min),
            Point3D::new(x_pos, y_max, z_min),
            Point3D::new(x_pos, y_max, z_max),
            Point3D::new(x_pos, y_min, z_max),
        ];

        let bounds = Box3D::new(x_pos, y_min, z_min, 0.0, width, height);

        Some(SharedFace3D {
            vertices,
            area,
            bounds,
            face_direction,
        })
    }

    /// 创建Y方向的共享面（XZ面）
    fn create_shared_face_y(&self, a: &Box3D, b: &Box3D) -> Option<SharedFace3D> {
        // 计算重叠区域
        let x_min = a.x.max(b.x);
        let x_max = (a.x + a.width).min(b.x + b.width);
        let z_min = a.z.max(b.z);
        let z_max = (a.z + a.depth).min(b.z + b.depth);

        if x_max <= x_min || z_max <= z_min {
            return None;
        }

        let y_pos = if a.y + a.height == b.y { a.y + a.height } else { b.y + b.height };
        let face_direction = if a.y + a.height == b.y {
            FaceDirection::YPositive
        } else {
            FaceDirection::YNegative
        };

        let width = x_max - x_min;
        let height = z_max - z_min;
        let area = width * height;

        let vertices = vec![
            Point3D::new(x_min, y_pos, z_min),
            Point3D::new(x_max, y_pos, z_min),
            Point3D::new(x_max, y_pos, z_max),
            Point3D::new(x_min, y_pos, z_max),
        ];

        let bounds = Box3D::new(x_min, y_pos, z_min, width, 0.0, height);

        Some(SharedFace3D {
            vertices,
            area,
            bounds,
            face_direction,
        })
    }

    /// 创建Z方向的共享面（XY面）
    fn create_shared_face_z(&self, a: &Box3D, b: &Box3D) -> Option<SharedFace3D> {
        // 计算重叠区域
        let x_min = a.x.max(b.x);
        let x_max = (a.x + a.width).min(b.x + b.width);
        let y_min = a.y.max(b.y);
        let y_max = (a.y + a.height).min(b.y + b.height);

        if x_max <= x_min || y_max <= y_min {
            return None;
        }

        let z_pos = if a.z + a.depth == b.z { a.z + a.depth } else { b.z + b.depth };
        let face_direction = if a.z + a.depth == b.z {
            FaceDirection::ZPositive
        } else {
            FaceDirection::ZNegative
        };

        let width = x_max - x_min;
        let height = y_max - y_min;
        let area = width * height;

        let vertices = vec![
            Point3D::new(x_min, y_min, z_pos),
            Point3D::new(x_max, y_min, z_pos),
            Point3D::new(x_max, y_max, z_pos),
            Point3D::new(x_min, y_max, z_pos),
        ];

        let bounds = Box3D::new(x_min, y_min, z_pos, width, height, 0.0);

        Some(SharedFace3D {
            vertices,
            area,
            bounds,
            face_direction,
        })
    }

    /// 漏斗算法 - 在管道内细化路径
    pub fn funnel_algorithm(&self, corridor_id: usize, start_point: Point3D, end_point: Point3D) -> Option<RefinedPath> {
        if corridor_id >= self.corridors.len() {
            return None;
        }

        let corridor = &self.corridors[corridor_id];
        let gate_ids = &corridor.gates;

        if gate_ids.is_empty() {
            // 没有门户，直接连接起点和终点
            let points = vec![start_point, end_point];
            let total_length = self.calculate_path_length(&points);
            return Some(RefinedPath {
                points,
                total_length,
                smoothness: 1.0,
            });
        }

        // 简化版漏斗算法：确保路径穿过每个门户，但尽可能直线化
        let optimized_path = self.compute_simplified_funnel_path(start_point, end_point, gate_ids);

        let total_length = self.calculate_path_length(&optimized_path);
        let smoothness = self.calculate_path_smoothness(&optimized_path);

        Some(RefinedPath {
            points: optimized_path,
            total_length,
            smoothness,
        })
    }

    /// 智能漏斗算法：在门户区域内寻找最优路径点
    fn compute_simplified_funnel_path(&self, start: Point3D, end: Point3D, gate_ids: &[usize]) -> Vec<Point3D> {
        if gate_ids.is_empty() {
            return vec![start, end];
        }

        // 收集所有门户信息
        let mut gates = Vec::new();
        for &gate_id in gate_ids {
            if gate_id < self.corridor_gates.len() {
                let gate = &self.corridor_gates[gate_id];
                gates.push(gate);
            }
        }

        if gates.is_empty() {
            return vec![start, end];
        }

        // 使用智能路径规划，在门户区域内寻找最优通过点
        self.compute_optimal_path_through_gates(start, end, &gates)
    }

    /// 简化但正确的路径优化 - 确保在管道内部
    fn compute_optimal_path_through_gates(&self, start: Point3D, end: Point3D, gates: &[&CorridorGate]) -> Vec<Point3D> {
        if gates.is_empty() {
            return vec![start, end];
        }

        // 第一步：生成基础路径（确保穿过所有门户）
        let mut base_path = vec![start.clone()];

        for gate in gates {
            // 对于每个门户，找到最优的穿过点
            let gate_point = self.find_smart_gate_point(&base_path.last().unwrap(), &end, gate);
            base_path.push(gate_point);
        }

        base_path.push(end);

        // 第二步：优化路径 - 尝试跳过不必要的点
        self.simplify_path_through_gates(base_path, gates)
    }

    /// 智能地在门户中选择通过点
    fn find_smart_gate_point(&self, from: &Point3D, final_target: &Point3D, gate: &CorridorGate) -> Point3D {
        // 计算从当前位置到最终目标的直线与门户的交点
        if let Some(intersection) = self.find_line_gate_intersection(from, final_target, gate) {
            if self.point_in_gate_bounds(&intersection, gate) {
                return intersection;
            }
        }

        // 如果没有合适的交点，在门户内寻找最接近直线的点
        let candidates = vec![
            gate.center_point.clone(),
            // 可以添加更多候选点，但保持简单
        ];

        let mut best_point = gate.center_point.clone();
        let mut min_distance = f32::MAX;

        for candidate in candidates {
            if self.point_in_gate_bounds(&candidate, gate) {
                let distance = self.point_to_line_distance(&candidate, from, final_target);
                if distance < min_distance {
                    min_distance = distance;
                    best_point = candidate;
                }
            }
        }

        best_point
    }

    /// 简化路径 - 移除不必要的中间点
    fn simplify_path_through_gates(&self, path: Vec<Point3D>, gates: &[&CorridorGate]) -> Vec<Point3D> {
        if path.len() <= 2 {
            return path;
        }

        let mut simplified = vec![path[0].clone()];
        let mut i = 0;

        while i < path.len() - 1 {
            let mut furthest = i + 1;

            // 尝试找到能直接到达的最远点
            for j in (i + 2)..path.len() {
                if self.can_connect_directly(&path[i], &path[j], gates, i, j) {
                    furthest = j;
                } else {
                    break;
                }
            }

            simplified.push(path[furthest].clone());
            i = furthest;
        }

        simplified
    }

    /// 检查两点之间是否可以直接连接（穿过所有必要的门户）
    fn can_connect_directly(&self, start: &Point3D, end: &Point3D, gates: &[&CorridorGate],
                            start_idx: usize, end_idx: usize) -> bool {
        // 如果跨越了多个路径点，需要检查是否穿过了所有中间的门户
        if end_idx <= start_idx + 1 {
            return true; // 相邻点总是可以连接
        }

        // 检查直线是否穿过所有中间的门户
        // 注意：这里的索引对应路径点，需要映射到门户
        let gate_start = if start_idx == 0 { 0 } else { start_idx - 1 };
        let gate_end = if end_idx >= gates.len() { gates.len() - 1 } else { end_idx - 1 };

        for gate_idx in gate_start..=gate_end {
            if gate_idx < gates.len() {
                if !self.line_passes_through_gate_area(start, end, gates[gate_idx]) {
                    return false;
                }
            }
        }

        true
    }

    /// 验证并修复路径，确保完全在管道内部
    fn validate_and_fix_path(&self, path: Vec<Point3D>, gates: &[&CorridorGate]) -> Vec<Point3D> {
        if path.len() < 2 {
            return path;
        }

        let mut validated_path = vec![path[0].clone()];

        for i in 1..path.len() {
            let segment_start = &path[i - 1];
            let segment_end = &path[i];

            // 检查这个路径段是否完全在管道内部
            if self.is_segment_in_corridor(segment_start, segment_end, gates) {
                validated_path.push(segment_end.clone());
            } else {
                // 路径段离开了管道，需要修复
                let fixed_points = self.fix_segment_through_corridor(segment_start, segment_end, gates);
                for point in fixed_points {
                    if self.points_significantly_different(validated_path.last().unwrap(), &point) {
                        validated_path.push(point);
                    }
                }
            }
        }

        validated_path
    }

    /// 检查路径段是否完全在管道内部
    fn is_segment_in_corridor(&self, start: &Point3D, end: &Point3D, gates: &[&CorridorGate]) -> bool {
        // 检查路径段是否穿过所有相关的门户
        for gate in gates {
            if !self.segment_passes_through_gate(start, end, gate) {
                return false;
            }
        }
        true
    }

    /// 检查路径段是否穿过门户
    fn segment_passes_through_gate(&self, start: &Point3D, end: &Point3D, gate: &CorridorGate) -> bool {
        // 计算路径段与门户平面的交点
        if let Some(intersection) = self.find_line_gate_intersection(start, end, gate) {
            // 检查交点是否在门户边界内
            return self.point_in_gate_bounds(&intersection, gate);
        }
        false
    }

    /// 修复穿过管道的路径段
    fn fix_segment_through_corridor(&self, start: &Point3D, end: &Point3D, gates: &[&CorridorGate]) -> Vec<Point3D> {
        let mut fixed_path = Vec::new();

        // 找到需要穿过的门户
        for gate in gates {
            if let Some(intersection) = self.find_line_gate_intersection(start, end, gate) {
                if self.point_in_gate_bounds(&intersection, gate) {
                    fixed_path.push(intersection);
                } else {
                    // 如果交点不在门户内，使用门户中心点
                    fixed_path.push(gate.center_point.clone());
                }
            }
        }

        // 添加终点
        fixed_path.push(end.clone());
        fixed_path
    }

    /// 检查漏斗更新是否有效
    fn is_funnel_update_valid(&self, apex: &Point3D, current_bound: &Point3D, new_bound: &Point3D, opposite_bound: &Point3D) -> bool {
        // 新边界点应该在当前漏斗内，且不会与对面边界交叉
        self.point_in_funnel(apex, current_bound, opposite_bound, new_bound) &&
            !self.lines_intersect_2d(apex, new_bound, apex, opposite_bound)
    }

    /// 检查点是否在漏斗内
    fn point_in_funnel(&self, apex: &Point3D, left: &Point3D, right: &Point3D, point: &Point3D) -> bool {
        // 简化为2D检查（XY平面）
        !self.is_point_left_of_line(point, apex, left) &&
            self.is_point_left_of_line(point, apex, right)
    }

    /// 检查两条直线是否相交（2D）
    fn lines_intersect_2d(&self, a1: &Point3D, a2: &Point3D, b1: &Point3D, b2: &Point3D) -> bool {
        let d1 = self.cross_product_2d(a1, a2, b1);
        let d2 = self.cross_product_2d(a1, a2, b2);
        let d3 = self.cross_product_2d(b1, b2, a1);
        let d4 = self.cross_product_2d(b1, b2, a2);

        (d1 > 0.0) != (d2 > 0.0) && (d3 > 0.0) != (d4 > 0.0)
    }

    /// 2D叉积
    fn cross_product_2d(&self, a: &Point3D, b: &Point3D, c: &Point3D) -> f32 {
        (b.x - a.x) * (c.y - a.y) - (b.y - a.y) * (c.x - a.x)
    }

    /// 在门户内找到最优通过点
    fn find_optimal_gate_point(&self, from: &Point3D, to: &Point3D, gate: &CorridorGate) -> Point3D {
        // 计算直线与门户平面的交点
        if let Some(intersection) = self.find_line_gate_intersection(from, to, gate) {
            // 确保交点在门户边界内
            if self.point_in_gate_bounds(&intersection, gate) {
                return intersection;
            }
        }

        // 如果没有合适的交点，在门户内寻找最接近直线的点
        let candidates = self.generate_gate_candidate_points(gate);

        // 找到距离直线最近的点
        let mut best_point = gate.center_point.clone();
        let mut min_distance = f32::MAX;

        for point in candidates {
            // 确保点在门户边界内
            if self.point_in_gate_bounds(&point, gate) {
                let distance = self.point_to_line_distance(&point, from, to);
                if distance < min_distance {
                    min_distance = distance;
                    best_point = point;
                }
            }
        }

        best_point
    }

    /// 生成门户内的候选点
    fn generate_gate_candidate_points(&self, gate: &CorridorGate) -> Vec<Point3D> {
        let mut candidates = vec![gate.center_point.clone()];
        let vertices = &gate.shared_face.vertices;

        if vertices.len() < 3 {
            return candidates;
        }

        // 添加顶点
        for vertex in vertices {
            candidates.push(Point3D::new(vertex.x, vertex.y, vertex.z));
        }

        // 添加边的中点
        for i in 0..vertices.len() {
            let next_i = (i + 1) % vertices.len();
            let mid_point = Point3D::new(
                (vertices[i].x + vertices[next_i].x) / 2.0,
                (vertices[i].y + vertices[next_i].y) / 2.0,
                (vertices[i].z + vertices[next_i].z) / 2.0,
            );
            candidates.push(mid_point);
        }

        candidates
    }

    /// 优化路径，尝试跳过不必要的中间点，但确保路径仍然穿过所有门户
    fn optimize_path_through_gates_new(&self, path: Vec<Point3D>, gates: &[&CorridorGate]) -> Vec<Point3D> {
        if path.len() <= 2 {
            return path;
        }

        let mut optimized = vec![path[0].clone()];
        let mut i = 0;

        while i < path.len() - 1 {
            let mut furthest_reachable = i + 1;

            // 尝试找到能直接到达的最远点
            for j in (i + 2)..path.len() {
                // 检查从当前点到目标点的直线是否穿过所有必要的门户
                let mut valid_path = true;

                // 对于路径中的每个门户，检查直线是否穿过
                for gate_idx in 0..gates.len() {
                    // 只检查路径段中可能包含的门户
                    if gate_idx >= i && gate_idx < j - 1 {
                        if !self.line_passes_through_gate_area(&path[i], &path[j], gates[gate_idx]) {
                            valid_path = false;
                            break;
                        }
                    }
                }

                if valid_path {
                    furthest_reachable = j;
                } else {
                    break;
                }
            }

            // 添加能到达的最远点
            optimized.push(path[furthest_reachable].clone());
            i = furthest_reachable;
        }

        optimized
    }

    /// 寻找从当前位置能到达的最远目标
    fn find_furthest_reachable_target(&self, current: &Point3D, end: &Point3D,
                                      gates: &[&CorridorGate], start_gate_idx: usize) -> FurthestTarget {
        // 首先检查是否可以直接到达终点
        if self.can_reach_end_directly(current, end, gates, start_gate_idx) {
            return FurthestTarget::EndPoint;
        }

        // 从最远的门户开始向前检查
        for gate_idx in (start_gate_idx..gates.len()).rev() {
            if self.can_reach_gate_skipping_intermediates(current, gates, start_gate_idx, gate_idx) {
                return FurthestTarget::Gate(gate_idx);
            }
        }

        // 如果都不行，至少要通过下一个门户
        FurthestTarget::Gate(start_gate_idx)
    }

    /// 检查是否可以直接到达终点（跳过所有剩余门户）
    fn can_reach_end_directly(&self, current: &Point3D, end: &Point3D,
                              gates: &[&CorridorGate], start_gate_idx: usize) -> bool {
        // 检查从当前位置到终点的直线是否穿过所有剩余门户
        for gate_idx in start_gate_idx..gates.len() {
            let gate = gates[gate_idx];
            if !self.line_passes_through_gate_area(current, end, gate) {
                return false;
            }
        }
        true
    }

    /// 检查是否可以跳过中间门户直接到达目标门户
    fn can_reach_gate_skipping_intermediates(&self, current: &Point3D, gates: &[&CorridorGate],
                                             start_gate_idx: usize, target_gate_idx: usize) -> bool {
        if target_gate_idx <= start_gate_idx {
            return false;
        }

        let target_gate = gates[target_gate_idx];

        // 计算到目标门户的最优点
        let target_point = self.find_best_point_in_gate_for_line(current, &target_gate.center_point, target_gate);

        // 检查这条路径是否穿过所有中间门户
        for gate_idx in start_gate_idx..target_gate_idx {
            let intermediate_gate = gates[gate_idx];
            if !self.line_passes_through_gate_area(current, &target_point, intermediate_gate) {
                return false;
            }
        }

        true
    }

    /// 在门户内寻找对于给定直线的最佳点
    fn find_best_point_in_gate_for_line(&self, line_start: &Point3D, line_end: &Point3D, gate: &CorridorGate) -> Point3D {
        // 首先尝试计算直线与门户平面的交点
        if let Some(intersection) = self.find_line_gate_intersection(line_start, line_end, gate) {
            if self.point_in_gate_bounds(&intersection, gate) {
                return intersection;
            }
        }

        // 如果直线不穿过门户，在门户内寻找最接近直线的点
        self.find_closest_valid_point_in_gate(line_start, line_end, gate)
    }

    /// 在门户内寻找最接近给定直线的有效点
    fn find_closest_valid_point_in_gate(&self, line_start: &Point3D, line_end: &Point3D, gate: &CorridorGate) -> Point3D {
        let vertices = &gate.shared_face.vertices;
        if vertices.len() < 4 {
            return gate.center_point.clone();
        }

        // 生成门户内的候选点
        let mut candidates = vec![gate.center_point.clone()];

        // 添加门户边界上的点
        for vertex in vertices {
            candidates.push(Point3D::new(vertex.x, vertex.y, vertex.z));
        }

        // 添加门户边界的中点
        for i in 0..vertices.len() {
            let next_i = (i + 1) % vertices.len();
            let mid_point = Point3D::new(
                (vertices[i].x + vertices[next_i].x) / 2.0,
                (vertices[i].y + vertices[next_i].y) / 2.0,
                (vertices[i].z + vertices[next_i].z) / 2.0,
            );
            candidates.push(mid_point);
        }

        // 在XY平面上添加更多采样点（如果门户足够大）
        let gate_bounds = self.get_gate_bounds(gate);
        if gate_bounds.is_some() {
            let (min_x, max_x, min_y, max_y, min_z, max_z) = gate_bounds.unwrap();

            // 在门户内部采样几个点
            let samples = 3; // 每个维度的采样数
            for i in 0..samples {
                for j in 0..samples {
                    let t_x = i as f32 / (samples - 1) as f32;
                    let t_y = j as f32 / (samples - 1) as f32;

                    let sample_x = min_x + t_x * (max_x - min_x);
                    let sample_y = min_y + t_y * (max_y - min_y);

                    // 对于Z坐标，尝试几个不同的高度
                    for k in 0..samples {
                        let t_z = k as f32 / (samples - 1) as f32;
                        let sample_z = min_z + t_z * (max_z - min_z);

                        let sample_point = Point3D::new(sample_x, sample_y, sample_z);
                        if self.point_in_gate_bounds(&sample_point, gate) {
                            candidates.push(sample_point);
                        }
                    }
                }
            }
        }

        // 找到距离直线最近的候选点
        let mut best_point = gate.center_point.clone();
        let mut min_distance = f32::MAX;

        for candidate in candidates {
            let distance = self.point_to_line_distance(&candidate, line_start, line_end);
            if distance < min_distance {
                min_distance = distance;
                best_point = candidate;
            }
        }

        best_point
    }

    /// 获取门户的边界框
    fn get_gate_bounds(&self, gate: &CorridorGate) -> Option<(f32, f32, f32, f32, f32, f32)> {
        let vertices = &gate.shared_face.vertices;
        if vertices.is_empty() {
            return None;
        }

        let min_x = vertices.iter().map(|v| v.x).fold(f32::INFINITY, f32::min);
        let max_x = vertices.iter().map(|v| v.x).fold(f32::NEG_INFINITY, f32::max);
        let min_y = vertices.iter().map(|v| v.y).fold(f32::INFINITY, f32::min);
        let max_y = vertices.iter().map(|v| v.y).fold(f32::NEG_INFINITY, f32::max);
        let min_z = vertices.iter().map(|v| v.z).fold(f32::INFINITY, f32::min);
        let max_z = vertices.iter().map(|v| v.z).fold(f32::NEG_INFINITY, f32::max);

        Some((min_x, max_x, min_y, max_y, min_z, max_z))
    }

    /// 在门户内寻找最接近直线的点
    fn find_closest_point_in_gate_to_line(&self, line_start: &Point3D, line_end: &Point3D, gate: &CorridorGate) -> Option<Point3D> {
        let vertices = &gate.shared_face.vertices;
        if vertices.len() < 4 {
            return None;
        }

        // 简化：在门户的几个关键点中选择最接近直线的点
        let candidate_points = vec![
            gate.center_point.clone(),
            // 可以添加门户边界的其他点
        ];

        let mut best_point = gate.center_point.clone();
        let mut min_distance = f32::MAX;

        for point in candidate_points {
            let distance = self.point_to_line_distance(&point, line_start, line_end);
            if distance < min_distance {
                min_distance = distance;
                best_point = point;
            }
        }

        Some(best_point)
    }

    /// 计算点到直线的距离
    fn point_to_line_distance(&self, point: &Point3D, line_start: &Point3D, line_end: &Point3D) -> f32 {
        let line_vec = Point3D::new(
            line_end.x - line_start.x,
            line_end.y - line_start.y,
            line_end.z - line_start.z,
        );

        let point_vec = Point3D::new(
            point.x - line_start.x,
            point.y - line_start.y,
            point.z - line_start.z,
        );

        let line_length_sq = line_vec.x * line_vec.x + line_vec.y * line_vec.y + line_vec.z * line_vec.z;

        if line_length_sq < 1e-6 {
            return ((point.x - line_start.x).powi(2) +
                (point.y - line_start.y).powi(2) +
                (point.z - line_start.z).powi(2)).sqrt();
        }

        let t = (point_vec.x * line_vec.x + point_vec.y * line_vec.y + point_vec.z * line_vec.z) / line_length_sq;
        let t = t.clamp(0.0, 1.0);

        let closest_on_line = Point3D::new(
            line_start.x + t * line_vec.x,
            line_start.y + t * line_vec.y,
            line_start.z + t * line_vec.z,
        );

        ((point.x - closest_on_line.x).powi(2) +
            (point.y - closest_on_line.y).powi(2) +
            (point.z - closest_on_line.z).powi(2)).sqrt()
    }

    /// 在门户内寻找最优通过点
    fn find_optimal_point_in_gate(&self, from: &Point3D, to: &Point3D, gate: &CorridorGate) -> Point3D {
        // 计算直线路径与门户平面的交点
        let intersection = self.find_line_gate_intersection(from, to, gate);

        // 如果交点在门户范围内，使用交点；否则使用门户中心点
        if let Some(point) = intersection {
            if self.point_in_gate_bounds(&point, gate) {
                return point;
            }
        }

        // 如果无法找到合适的交点，使用门户中心点，但尝试优化高度
        let mut optimal_point = gate.center_point.clone();

        // 尝试保持更平滑的高度变化
        let height_from_line = self.interpolate_height_on_line(from, to, &optimal_point);
        if let Some(interpolated_height) = height_from_line {
            // 检查插值高度是否在门户的高度范围内
            let gate_bounds = self.get_gate_height_bounds(gate);
            if interpolated_height >= gate_bounds.0 && interpolated_height <= gate_bounds.1 {
                optimal_point.z = interpolated_height;
            }
        }

        optimal_point
    }

    /// 计算直线与门户平面的交点
    fn find_line_gate_intersection(&self, from: &Point3D, to: &Point3D, gate: &CorridorGate) -> Option<Point3D> {
        // 简化版本：假设门户是垂直的平面
        let gate_center = &gate.center_point;
        let face_direction = &gate.shared_face.face_direction;

        // 根据面的方向确定平面法向量和平面上的点
        let (plane_normal, plane_point) = match face_direction {
            FaceDirection::XPositive | FaceDirection::XNegative => {
                (Point3D::new(1.0, 0.0, 0.0), Point3D::new(gate_center.x, 0.0, 0.0))
            },
            FaceDirection::YPositive | FaceDirection::YNegative => {
                (Point3D::new(0.0, 1.0, 0.0), Point3D::new(0.0, gate_center.y, 0.0))
            },
            FaceDirection::ZPositive | FaceDirection::ZNegative => {
                (Point3D::new(0.0, 0.0, 1.0), Point3D::new(0.0, 0.0, gate_center.z))
            },
        };

        // 计算直线与平面的交点
        let line_dir = Point3D::new(to.x - from.x, to.y - from.y, to.z - from.z);
        let denom = line_dir.x * plane_normal.x + line_dir.y * plane_normal.y + line_dir.z * plane_normal.z;

        if denom.abs() < 1e-6 {
            return None; // 直线平行于平面
        }

        let t = ((plane_point.x - from.x) * plane_normal.x +
            (plane_point.y - from.y) * plane_normal.y +
            (plane_point.z - from.z) * plane_normal.z) / denom;

        if t < 0.0 || t > 1.0 {
            return None; // 交点不在线段上
        }

        Some(Point3D::new(
            from.x + t * line_dir.x,
            from.y + t * line_dir.y,
            from.z + t * line_dir.z,
        ))
    }

    /// 检查点是否在门户边界内
    fn point_in_gate_bounds(&self, point: &Point3D, gate: &CorridorGate) -> bool {
        let vertices = &gate.shared_face.vertices;
        if vertices.len() < 4 {
            return false;
        }

        // 简化检查：使用边界框
        let min_x = vertices.iter().map(|v| v.x).fold(f32::INFINITY, f32::min);
        let max_x = vertices.iter().map(|v| v.x).fold(f32::NEG_INFINITY, f32::max);
        let min_y = vertices.iter().map(|v| v.y).fold(f32::INFINITY, f32::min);
        let max_y = vertices.iter().map(|v| v.y).fold(f32::NEG_INFINITY, f32::max);
        let min_z = vertices.iter().map(|v| v.z).fold(f32::INFINITY, f32::min);
        let max_z = vertices.iter().map(|v| v.z).fold(f32::NEG_INFINITY, f32::max);

        point.x >= min_x && point.x <= max_x &&
            point.y >= min_y && point.y <= max_y &&
            point.z >= min_z && point.z <= max_z
    }

    /// 在直线上插值高度
    fn interpolate_height_on_line(&self, from: &Point3D, to: &Point3D, point: &Point3D) -> Option<f32> {
        let line_length_xy = ((to.x - from.x).powi(2) + (to.y - from.y).powi(2)).sqrt();
        if line_length_xy < 1e-6 {
            return None;
        }

        let point_distance_xy = ((point.x - from.x).powi(2) + (point.y - from.y).powi(2)).sqrt();
        let t = point_distance_xy / line_length_xy;

        if t >= 0.0 && t <= 1.0 {
            Some(from.z + t * (to.z - from.z))
        } else {
            None
        }
    }

    /// 获取门户的高度范围
    fn get_gate_height_bounds(&self, gate: &CorridorGate) -> (f32, f32) {
        let vertices = &gate.shared_face.vertices;
        if vertices.is_empty() {
            return (gate.center_point.z - 0.5, gate.center_point.z + 0.5);
        }

        let min_z = vertices.iter().map(|v| v.z).fold(f32::INFINITY, f32::min);
        let max_z = vertices.iter().map(|v| v.z).fold(f32::NEG_INFINITY, f32::max);

        (min_z, max_z)
    }

    /// 在下一个门户中寻找最优点
    fn find_optimal_point_in_next_gate(&self, current: &Point3D, final_target: &Point3D,
                                       gates: &[&CorridorGate], next_gate_idx: usize) -> Point3D {
        if next_gate_idx >= gates.len() {
            return final_target.clone();
        }

        let next_gate = gates[next_gate_idx];

        // 尝试在下一个门户中找到朝向最终目标的最优点
        self.find_optimal_point_in_gate(current, final_target, next_gate)
    }

    /// 检查两点是否有显著差异
    fn points_significantly_different(&self, p1: &Point3D, p2: &Point3D) -> bool {
        let threshold = 0.1; // 可调整的阈值
        let distance = ((p1.x - p2.x).powi(2) + (p1.y - p2.y).powi(2) + (p1.z - p2.z).powi(2)).sqrt();
        distance > threshold
    }

    /// 优化路径点序列，在保证穿过门户的前提下减少不必要的转折
    fn optimize_waypoint_path(&self, waypoints: &[Point3D], gates: &[&CorridorGate]) -> Vec<Point3D> {
        if waypoints.len() <= 2 {
            return waypoints.to_vec();
        }

        let mut optimized = vec![waypoints[0].clone()];
        let mut current_idx = 0;

        while current_idx < waypoints.len() - 1 {
            // 尝试跳过中间点，找到最远可达的点
            let mut furthest_idx = current_idx + 1;

            for target_idx in (current_idx + 2)..waypoints.len() {
                // 检查从当前点到目标点的直线是否可行
                if self.is_direct_path_valid(&waypoints[current_idx], &waypoints[target_idx],
                                             current_idx, target_idx, gates) {
                    furthest_idx = target_idx;
                } else {
                    break;
                }
            }

            // 移动到最远可达的点
            optimized.push(waypoints[furthest_idx].clone());
            current_idx = furthest_idx;
        }

        optimized
    }

    /// 检查直接路径是否有效（穿过所有必要的门户）
    fn is_direct_path_valid(&self, start: &Point3D, end: &Point3D, start_idx: usize, end_idx: usize,
                            gates: &[&CorridorGate]) -> bool {
        // 如果跨越了门户，需要检查路径是否穿过这些门户
        if end_idx > start_idx + 1 {
            // 检查中间的门户
            for gate_idx in (start_idx + 1)..end_idx {
                if gate_idx <= gates.len() {
                    let gate_idx_in_array = gate_idx - 1; // 调整索引
                    if gate_idx_in_array < gates.len() {
                        let gate = gates[gate_idx_in_array];
                        if !self.line_passes_through_gate_area(start, end, gate) {
                            return false;
                        }
                    }
                }
            }
        }

        true
    }

    /// 检查直线是否穿过门户区域
    fn line_passes_through_gate_area(&self, line_start: &Point3D, line_end: &Point3D, gate: &CorridorGate) -> bool {
        let gate_center = &gate.center_point;

        // 计算直线上最接近门户中心的点
        let line_dir = Point3D::new(
            line_end.x - line_start.x,
            line_end.y - line_start.y,
            line_end.z - line_start.z,
        );

        let line_length = (line_dir.x * line_dir.x + line_dir.y * line_dir.y + line_dir.z * line_dir.z).sqrt();

        if line_length < 1e-6 {
            return false;
        }

        let line_dir_normalized = Point3D::new(
            line_dir.x / line_length,
            line_dir.y / line_length,
            line_dir.z / line_length,
        );

        // 投影门户中心到直线上
        let to_gate = Point3D::new(
            gate_center.x - line_start.x,
            gate_center.y - line_start.y,
            gate_center.z - line_start.z,
        );

        let projection_length = to_gate.x * line_dir_normalized.x +
            to_gate.y * line_dir_normalized.y +
            to_gate.z * line_dir_normalized.z;

        // 确保投影点在线段内
        if projection_length < 0.0 || projection_length > line_length {
            return false;
        }

        // 计算最近点
        let closest_point = Point3D::new(
            line_start.x + line_dir_normalized.x * projection_length,
            line_start.y + line_dir_normalized.y * projection_length,
            line_start.z + line_dir_normalized.z * projection_length,
        );

        // 检查距离是否在门户的有效范围内
        let distance = ((gate_center.x - closest_point.x).powi(2) +
            (gate_center.y - closest_point.y).powi(2) +
            (gate_center.z - closest_point.z).powi(2)).sqrt();

        // 使用门户的有效面积来确定阈值
        let threshold = (gate.effective_area / 4.0).sqrt(); // 假设门户是正方形
        let min_threshold = 0.5; // 最小阈值
        let max_threshold = 2.0; // 最大阈值

        let effective_threshold = threshold.clamp(min_threshold, max_threshold);

        distance <= effective_threshold
    }

    /// 检查是否可以通过剩余门户直接到达目标
    fn can_reach_target_through_remaining_gates(&self, start: &Point3D, target: &Point3D, remaining_gates: &[Point3D]) -> bool {
        // 简化检查：如果剩余门户不多，允许直接到达
        if remaining_gates.len() <= 2 {
            return true;
        }

        // 检查路径是否接近所有剩余门户
        for gate_center in remaining_gates {
            if !self.line_passes_near_point(start, target, gate_center, 1.5) {
                return false;
            }
        }

        true
    }

    /// 检查是否可以直接到达某个门户
    fn can_reach_gate_directly(&self, start: &Point3D, target_gate: &Point3D, intermediate_gates: &[Point3D]) -> bool {
        // 检查路径是否接近所有中间门户
        for gate_center in intermediate_gates {
            if !self.line_passes_near_point(start, target_gate, gate_center, 1.0) {
                return false;
            }
        }

        true
    }

    /// 检查直线是否接近某个点
    fn line_passes_near_point(&self, line_start: &Point3D, line_end: &Point3D, point: &Point3D, threshold: f32) -> bool {
        // 计算点到直线的距离
        let line_dir = Point3D::new(
            line_end.x - line_start.x,
            line_end.y - line_start.y,
            line_end.z - line_start.z,
        );

        let line_length = (line_dir.x * line_dir.x + line_dir.y * line_dir.y + line_dir.z * line_dir.z).sqrt();

        if line_length < 1e-6 {
            return false;
        }

        let line_dir_normalized = Point3D::new(
            line_dir.x / line_length,
            line_dir.y / line_length,
            line_dir.z / line_length,
        );

        // 投影点到直线上
        let to_point = Point3D::new(
            point.x - line_start.x,
            point.y - line_start.y,
            point.z - line_start.z,
        );

        let projection_length = to_point.x * line_dir_normalized.x +
            to_point.y * line_dir_normalized.y +
            to_point.z * line_dir_normalized.z;

        // 确保投影点在线段内
        if projection_length < 0.0 || projection_length > line_length {
            return false;
        }

        // 计算最近点
        let closest_point = Point3D::new(
            line_start.x + line_dir_normalized.x * projection_length,
            line_start.y + line_dir_normalized.y * projection_length,
            line_start.z + line_dir_normalized.z * projection_length,
        );

        // 检查距离
        let distance = ((point.x - closest_point.x).powi(2) +
            (point.y - closest_point.y).powi(2) +
            (point.z - closest_point.z).powi(2)).sqrt();

        distance <= threshold
    }

    /// 提取门户的边界点
    fn extract_gate_boundaries(&self, gate: &CorridorGate) -> (Point3D, Point3D) {
        let vertices = &gate.shared_face.vertices;

        if vertices.len() >= 4 {
            // 使用矩形的对角顶点作为左右边界
            let v1 = &vertices[0];
            let v3 = &vertices[2]; // 对角顶点

            let left_point = Point3D::new(v1.x, v1.y, v1.z);
            let right_point = Point3D::new(v3.x, v3.y, v3.z);

            (left_point, right_point)
        } else {
            // 如果顶点不足，使用中心点的偏移
            let center = &gate.center_point;
            let offset = 0.5;

            let left_point = Point3D::new(center.x - offset, center.y, center.z);
            let right_point = Point3D::new(center.x + offset, center.y, center.z);

            (left_point, right_point)
        }
    }

    /// 计算漏斗路径
    fn compute_funnel_path(&self, start: Point3D, end: Point3D, gates: &[FunnelGate]) -> Vec<Point3D> {
        let mut path = vec![start.clone()];

        if gates.is_empty() {
            path.push(end);
            return path;
        }

        // 初始化漏斗
        let mut funnel_apex = start;
        let mut funnel_left = gates[0].left.clone();
        let mut funnel_right = gates[0].right.clone();
        let mut apex_index = 0;

        for (i, gate) in gates.iter().enumerate() {
            // 更新左边界
            if !self.is_point_left_of_line(&gate.left, &funnel_apex, &funnel_left) {
                // 新的左边界点在当前漏斗内部
                if self.is_point_left_of_line(&gate.left, &funnel_apex, &funnel_right) {
                    // 新左边界点在右边界左侧，更新左边界
                    funnel_left = gate.left.clone();
                } else {
                    // 新左边界点在右边界右侧，需要收缩漏斗
                    path.push(funnel_right.clone());
                    funnel_apex = funnel_right.clone();
                    funnel_left = gate.left.clone();
                    funnel_right = gate.right.clone();
                    apex_index = i;
                    continue;
                }
            }

            // 更新右边界
            if self.is_point_left_of_line(&gate.right, &funnel_apex, &funnel_right) {
                // 新的右边界点在当前漏斗内部
                if !self.is_point_left_of_line(&gate.right, &funnel_apex, &funnel_left) {
                    // 新右边界点在左边界右侧，更新右边界
                    funnel_right = gate.right.clone();
                } else {
                    // 新右边界点在左边界左侧，需要收缩漏斗
                    path.push(funnel_left.clone());
                    funnel_apex = funnel_left.clone();
                    funnel_left = gate.left.clone();
                    funnel_right = gate.right.clone();
                    apex_index = i;
                }
            }
        }

        // 处理到终点的路径
        if self.can_reach_directly(&funnel_apex, &end, &funnel_left, &funnel_right) {
            path.push(end);
        } else {
            // 需要通过最后的边界点
            if self.is_point_left_of_line(&end, &funnel_apex, &funnel_left) {
                path.push(funnel_left);
            } else {
                path.push(funnel_right);
            }
            path.push(end);
        }

        path
    }

    /// 判断点是否在线段的左侧（3D版本，简化为XY平面）
    fn is_point_left_of_line(&self, point: &Point3D, line_start: &Point3D, line_end: &Point3D) -> bool {
        // 使用叉积判断，简化为XY平面
        let v1_x = line_end.x - line_start.x;
        let v1_y = line_end.y - line_start.y;
        let v2_x = point.x - line_start.x;
        let v2_y = point.y - line_start.y;

        let cross_product = v1_x * v2_y - v1_y * v2_x;
        cross_product > 0.0
    }

    /// 检查是否可以直接到达终点
    fn can_reach_directly(&self, apex: &Point3D, target: &Point3D, left: &Point3D, right: &Point3D) -> bool {
        // 检查目标点是否在当前漏斗内
        !self.is_point_left_of_line(target, apex, left) &&
            self.is_point_left_of_line(target, apex, right)
    }

    /// 优化路径，在保证穿过门户的前提下尽可能直线化
    fn optimize_path_through_gates(&self, path: &[Point3D], gates: &[&CorridorGate]) -> Vec<Point3D> {
        if path.len() <= 2 {
            return path.to_vec();
        }

        let mut optimized = vec![path[0].clone()];
        let mut i = 0;

        while i < path.len() - 1 {
            let current = &path[i];

            // 尝试跳过中间点，直接连接到更远的点
            let mut furthest_reachable = i + 1;

            for j in (i + 2)..path.len() {
                // 检查从current到path[j]的直线是否可行
                if self.is_path_valid_through_gates(current, &path[j], gates, i + 1, j - 1) {
                    furthest_reachable = j;
                } else {
                    break;
                }
            }

            // 添加能到达的最远点
            if furthest_reachable < path.len() {
                optimized.push(path[furthest_reachable].clone());
                i = furthest_reachable;
            } else {
                break;
            }
        }

        // 确保终点被包含
        if let Some(last) = path.last() {
            if let Some(opt_last) = optimized.last() {
                if opt_last.x != last.x || opt_last.y != last.y || opt_last.z != last.z {
                    optimized.push(last.clone());
                }
            }
        }

        optimized
    }

    /// 检查从start到end的直线路径是否穿过所有必要的门户
    fn is_path_valid_through_gates(&self, start: &Point3D, end: &Point3D, gates: &[&CorridorGate],
                                   gate_start_idx: usize, gate_end_idx: usize) -> bool {
        // 检查路径是否与中间的门户相交
        for i in gate_start_idx..=gate_end_idx {
            if i < gates.len() {
                let gate = gates[i];
                if !self.line_passes_through_gate(start, end, gate) {
                    return false;
                }
            }
        }
        true
    }

    /// 检查直线是否穿过门户
    fn line_passes_through_gate(&self, line_start: &Point3D, line_end: &Point3D, gate: &CorridorGate) -> bool {
        let gate_center = &gate.center_point;

        // 计算直线上最接近门户中心的点
        let line_dir = Point3D::new(
            line_end.x - line_start.x,
            line_end.y - line_start.y,
            line_end.z - line_start.z,
        );

        let line_length = (line_dir.x * line_dir.x + line_dir.y * line_dir.y + line_dir.z * line_dir.z).sqrt();

        if line_length < 1e-6 {
            return false;
        }

        let line_dir_normalized = Point3D::new(
            line_dir.x / line_length,
            line_dir.y / line_length,
            line_dir.z / line_length,
        );

        // 投影门户中心到直线上
        let to_gate = Point3D::new(
            gate_center.x - line_start.x,
            gate_center.y - line_start.y,
            gate_center.z - line_start.z,
        );

        let projection_length = to_gate.x * line_dir_normalized.x +
            to_gate.y * line_dir_normalized.y +
            to_gate.z * line_dir_normalized.z;

        // 确保投影点在线段内
        if projection_length < 0.0 || projection_length > line_length {
            return false;
        }

        // 计算最近点
        let closest_point = Point3D::new(
            line_start.x + line_dir_normalized.x * projection_length,
            line_start.y + line_dir_normalized.y * projection_length,
            line_start.z + line_dir_normalized.z * projection_length,
        );

        // 检查距离是否足够近
        let distance = ((gate_center.x - closest_point.x).powi(2) +
            (gate_center.y - closest_point.y).powi(2) +
            (gate_center.z - closest_point.z).powi(2)).sqrt();

        let threshold = 1.0; // 可调整的阈值
        distance <= threshold
    }

    /// 计算路径长度
    fn calculate_path_length(&self, path: &[Point3D]) -> f32 {
        if path.len() < 2 {
            return 0.0;
        }

        let mut total_length = 0.0;
        for i in 0..path.len() - 1 {
            let dx = path[i + 1].x - path[i].x;
            let dy = path[i + 1].y - path[i].y;
            let dz = path[i + 1].z - path[i].z;
            total_length += (dx * dx + dy * dy + dz * dz).sqrt();
        }
        total_length
    }

    /// 计算路径平滑度
    fn calculate_path_smoothness(&self, path: &[Point3D]) -> f32 {
        if path.len() < 3 {
            return 1.0;
        }

        let mut total_angle_change = 0.0;
        let mut segment_count = 0;

        for i in 1..path.len() - 1 {
            let v1 = Point3D::new(
                path[i].x - path[i - 1].x,
                path[i].y - path[i - 1].y,
                path[i].z - path[i - 1].z,
            );
            let v2 = Point3D::new(
                path[i + 1].x - path[i].x,
                path[i + 1].y - path[i].y,
                path[i + 1].z - path[i].z,
            );

            let len1 = (v1.x * v1.x + v1.y * v1.y + v1.z * v1.z).sqrt();
            let len2 = (v2.x * v2.x + v2.y * v2.y + v2.z * v2.z).sqrt();

            if len1 > 1e-6 && len2 > 1e-6 {
                let dot_product = (v1.x * v2.x + v1.y * v2.y + v1.z * v2.z) / (len1 * len2);
                let angle = dot_product.clamp(-1.0, 1.0).acos();
                total_angle_change += angle;
                segment_count += 1;
            }
        }

        if segment_count > 0 {
            // 平滑度 = 1 - (平均角度变化 / π)
            1.0 - (total_angle_change / segment_count as f32) / std::f32::consts::PI
        } else {
            1.0
        }
    }

    /// 从路径创建管道
    pub fn create_corridor_from_path(&mut self, path: &[usize], start_point: Point3D, end_point: Point3D) -> Option<usize> {
        if path.len() < 2 {
            return None;
        }

        let mut corridor_gates = Vec::new();
        let mut total_length = 0.0;
        let mut min_cross_section = f32::MAX;

        // 找到路径中相邻网格之间的门
        for i in 0..path.len() - 1 {
            let cell_a = path[i];
            let cell_b = path[i + 1];

            // 查找连接这两个网格的门
            if let Some(gate) = self.corridor_gates.iter().find(|g|
                (g.cell_a == cell_a && g.cell_b == cell_b) ||
                    (g.cell_a == cell_b && g.cell_b == cell_a)
            ) {
                corridor_gates.push(gate.id);
                total_length += gate.calculate_traversal_cost();
                min_cross_section = min_cross_section.min(gate.effective_area);
            }
        }

        if corridor_gates.is_empty() {
            return None;
        }

        let corridor_id = self.corridors.len();
        let corridor = Corridor {
            id: corridor_id,
            gates: corridor_gates,
            cells: path.to_vec(),
            total_length,
            min_cross_section,
            start_point,
            end_point,
        };

        self.corridors.push(corridor);
        Some(corridor_id)
    }

    /// 找到包含指定3D点的网格单元 - 优化版本
    pub fn find_cell_containing_point(&self, x: f32, y: f32, z: f32) -> Option<usize> {
        // 首先检查边界
        if x < 0.0 || y < 0.0 || z < 0.0 ||
            x >= self.map_size as f32 || y >= self.map_size as f32 || z >= self.map_height as f32 {
            return None;
        }

        // 优先检查较小的网格单元（更精确）
        for (i, cell) in self.cells.iter().enumerate() {
            if cell.node_type != NodeType::Blocked && cell.bounds.contains_point(x, y, z) {
                return Some(i);
            }
        }
        None
    }

    /// 3D A*路径规划算法
    pub fn find_path(&self, start_x: f32, start_y: f32, start_z: f32,
                     goal_x: f32, goal_y: f32, goal_z: f32) -> Option<Vec<usize>> {
        // 找到起点和终点所在的网格单元
        let start_cell = self.find_cell_containing_point(start_x, start_y, start_z)?;
        let goal_cell = self.find_cell_containing_point(goal_x, goal_y, goal_z)?;

        if start_cell == goal_cell {
            return Some(vec![start_cell]);
        }

        let mut open_set = BinaryHeap::new();
        let mut closed_set = HashSet::new();
        let mut came_from: HashMap<usize, usize> = HashMap::new();
        let mut g_score: HashMap<usize, f32> = HashMap::new();

        // 初始化起点
        g_score.insert(start_cell, 0.0);
        let h_cost = self.heuristic(start_cell, goal_cell);
        open_set.push(PathNode {
            cell_id: start_cell,
            g_cost: 0.0,
            h_cost,
            parent: None,
        });

        while let Some(current_node) = open_set.pop() {
            let current_cell = current_node.cell_id;

            if current_cell == goal_cell {
                // 重建路径
                return Some(self.reconstruct_path(&came_from, current_cell));
            }

            closed_set.insert(current_cell);

            // 检查所有邻居
            if let Some(neighbors) = self.adjacency.get(&current_cell) {
                for &(neighbor_cell, edge_cost) in neighbors {
                    if closed_set.contains(&neighbor_cell) {
                        continue;
                    }

                    let tentative_g = current_node.g_cost + edge_cost;

                    let is_better = if let Some(&existing_g) = g_score.get(&neighbor_cell) {
                        tentative_g < existing_g
                    } else {
                        true
                    };

                    if is_better {
                        came_from.insert(neighbor_cell, current_cell);
                        g_score.insert(neighbor_cell, tentative_g);

                        let h_cost = self.heuristic(neighbor_cell, goal_cell);
                        open_set.push(PathNode {
                            cell_id: neighbor_cell,
                            g_cost: tentative_g,
                            h_cost,
                            parent: Some(current_cell),
                        });
                    }
                }
            }
        }

        None // 没有找到路径
    }

    /// 3D启发式函数（欧几里得距离）
    fn heuristic(&self, cell_a: usize, cell_b: usize) -> f32 {
        let center_a = self.cells[cell_a].bounds.center();
        let center_b = self.cells[cell_b].bounds.center();

        let dx = center_a.0 - center_b.0;
        let dy = center_a.1 - center_b.1;
        let dz = center_a.2 - center_b.2;
        (dx * dx + dy * dy + dz * dz).sqrt()
    }

    /// 重建路径
    fn reconstruct_path(&self, came_from: &HashMap<usize, usize>, mut current: usize) -> Vec<usize> {
        let mut path = vec![current];

        while let Some(&parent) = came_from.get(&current) {
            current = parent;
            path.push(current);
        }

        path.reverse();
        path
    }

    /// 导出体素数据用于Python可视化
    pub fn export_voxel_data(&self) -> VoxelData3D {
        let size_xy = self.map_size;
        let size_z = self.map_height;
        let mut voxels = vec![0u8; (size_xy * size_xy * size_z) as usize];

        // 直接根据网格单元类型来标记体素，确保一致性
        for cell in &self.cells {
            let bounds = &cell.bounds;
            let start_x = bounds.x as u32;
            let start_y = bounds.y as u32;
            let start_z = bounds.z as u32;
            let end_x = (bounds.x + bounds.width) as u32;
            let end_y = (bounds.y + bounds.height) as u32;
            let end_z = (bounds.z + bounds.depth) as u32;

            let voxel_value = match cell.node_type {
                NodeType::Empty => 1,     // 绿色边框 - 空旷网格
                NodeType::Blocked => 3,   // 红色边框 + 黑色填充 - 被占据网格
                NodeType::Mixed => {
                    // 理论上不应该有Mixed类型的叶子节点
                    eprintln!("警告: 发现Mixed类型的网格单元，这不应该发生！");
                    1  // 当作空旷处理
                }
            };

            // 填充整个网格单元
            for x in start_x..end_x {
                for y in start_y..end_y {
                    for z in start_z..end_z {
                        if x < size_xy && y < size_xy && z < size_z {
                            let index = (z * size_xy * size_xy + y * size_xy + x) as usize;
                            voxels[index] = voxel_value;
                        }
                    }
                }
            }
        }

        // 收集网格边界信息
        let grid_boundaries: Vec<GridBoundary3D> = self.cells.iter().map(|cell| {
            GridBoundary3D {
                x: cell.bounds.x,
                y: cell.bounds.y,
                z: cell.bounds.z,
                width: cell.bounds.width,
                height: cell.bounds.height,
                depth: cell.bounds.depth,
                level: cell.level,
                node_type: cell.node_type,
            }
        }).collect();

        VoxelData3D {
            size_xy,
            size_z,
            voxels,
            grid_boundaries,
            path_points: None,  // 暂时为空，稍后添加路径数据
            corridor_gates: Some(self.corridor_gates.clone()),
            corridors: Some(self.corridors.clone()),
            refined_path: None,  // 暂时为空，稍后添加细化路径数据
        }
    }

    /// 智能路径细化 - 优先使用门优化，必要时使用虚拟网格
    pub fn refine_path_intelligently(&self, coarse_path: &[usize], start_point: Point3D, end_point: Point3D) -> Option<RefinedPath> {
        if coarse_path.is_empty() {
            return None;
        }

        // 首先尝试基于门的路径优化
        if let Some(corridor_id) = self.find_corridor_for_path(coarse_path) {
            if let Some(optimized_path) = self.optimize_path_through_gates_globally(corridor_id, start_point.clone(), end_point.clone()) {
                println!("使用门优化路径，点数: {}", optimized_path.points.len());
                return Some(optimized_path);
            }
        }

        // 如果门优化失败，回退到虚拟网格方法
        println!("门优化失败，使用虚拟网格方法");
        self.refine_path_with_virtual_grid(coarse_path, start_point, end_point)
    }

    /// 虚拟细网格路径细化 - 在粗路径覆盖的区域内使用1x1x1网格进行A*路径规划
    pub fn refine_path_with_virtual_grid(&self, coarse_path: &[usize], start_point: Point3D, end_point: Point3D) -> Option<RefinedPath> {
        if coarse_path.is_empty() {
            return None;
        }

        // 收集粗路径覆盖的所有网格单元
        let mut coarse_cells = HashSet::new();
        for &cell_id in coarse_path {
            if cell_id < self.cells.len() {
                coarse_cells.insert(cell_id);
            }
        }

        // 将起点和终点转换为虚拟网格坐标
        let start_node = VirtualGridNode::new(
            start_point.x.floor() as i32,
            start_point.y.floor() as i32,
            start_point.z.floor() as i32,
        );
        let end_node = VirtualGridNode::new(
            end_point.x.floor() as i32,
            end_point.y.floor() as i32,
            end_point.z.floor() as i32,
        );

        // 在虚拟细网格中进行A*路径规划
        let virtual_astar_start = Instant::now();
        let virtual_path = self.virtual_grid_astar(&start_node, &end_node, &coarse_cells)?;
        let virtual_astar_duration = virtual_astar_start.elapsed();
        println!("虚拟网格A*路径规划耗时: {:.2?}", virtual_astar_duration);

        // 将虚拟网格路径转换为Point3D路径
        let mut refined_points = Vec::new();
        refined_points.push(start_point); // 保留精确的起点

        for node in &virtual_path[1..virtual_path.len()-1] {
            refined_points.push(node.to_point3d());
        }

        refined_points.push(end_point); // 保留精确的终点

        // 计算路径长度
        let total_length = self.calculate_path_length(&refined_points);

        // 计算平滑度（简单的角度变化指标）
        let smoothness = self.calculate_path_smoothness(&refined_points);

        Some(RefinedPath {
            points: refined_points,
            total_length,
            smoothness,
        })
    }

    /// 在虚拟细网格中进行A*路径规划
    fn virtual_grid_astar(&self, start: &VirtualGridNode, goal: &VirtualGridNode,
                          allowed_coarse_cells: &HashSet<usize>) -> Option<Vec<VirtualGridNode>> {
        let mut open_set = BinaryHeap::new();
        let mut closed_set = HashSet::new();
        let mut came_from: HashMap<VirtualGridNode, VirtualGridNode> = HashMap::new();
        let mut g_score: HashMap<VirtualGridNode, f32> = HashMap::new();

        // 初始化起点
        g_score.insert(start.clone(), 0.0);
        let h_cost = start.distance_to(goal);
        open_set.push(VirtualPathNode {
            node: start.clone(),
            g_cost: 0.0,
            h_cost,
            parent: None,
        });

        while let Some(current_node) = open_set.pop() {
            let current = current_node.node;

            if current == *goal {
                // 重构路径
                let mut path = Vec::new();
                let mut current_pos = current;
                path.push(current_pos.clone());

                while let Some(parent) = came_from.get(&current_pos) {
                    current_pos = parent.clone();
                    path.push(current_pos.clone());
                }

                path.reverse();
                return Some(path);
            }

            closed_set.insert(current.clone());

            // 检查所有邻居（使用6邻域以减少计算量）
            for neighbor in current.get_neighbors_6() {
                if closed_set.contains(&neighbor) {
                    continue;
                }

                // 检查邻居是否在允许的粗网格区域内
                if !self.is_virtual_node_in_coarse_cells(&neighbor, allowed_coarse_cells) {
                    continue;
                }

                let tentative_g = current_node.g_cost + current.distance_to(&neighbor);

                let is_better = if let Some(&existing_g) = g_score.get(&neighbor) {
                    tentative_g < existing_g
                } else {
                    true
                };

                if is_better {
                    came_from.insert(neighbor.clone(), current.clone());
                    g_score.insert(neighbor.clone(), tentative_g);

                    let h_cost = neighbor.distance_to(goal);
                    open_set.push(VirtualPathNode {
                        node: neighbor,
                        g_cost: tentative_g,
                        h_cost,
                        parent: Some(current.clone()),
                    });
                }
            }
        }

        None // 没有找到路径
    }

    /// 检查虚拟网格节点是否在允许的粗网格单元内
    fn is_virtual_node_in_coarse_cells(&self, node: &VirtualGridNode, allowed_cells: &HashSet<usize>) -> bool {
        let point = node.to_point3d();

        for &cell_id in allowed_cells {
            if cell_id < self.cells.len() {
                let cell = &self.cells[cell_id];
                if cell.bounds.contains_point(point.x, point.y, point.z) {
                    return true;
                }
            }
        }

        false
    }

    /// 为给定路径找到对应的管道
    fn find_corridor_for_path(&self, coarse_path: &[usize]) -> Option<usize> {
        // 查找包含这些网格单元的管道
        for (corridor_id, corridor) in self.corridors.iter().enumerate() {
            if corridor.cells.len() >= coarse_path.len() {
                // 检查路径是否是管道的子集
                let mut path_matches = true;
                for &cell_id in coarse_path {
                    if !corridor.cells.contains(&cell_id) {
                        path_matches = false;
                        break;
                    }
                }
                if path_matches {
                    return Some(corridor_id);
                }
            }
        }
        None
    }

    /// 全局门路径优化 - 考虑高度连续性和总距离
    fn optimize_path_through_gates_globally(&self, corridor_id: usize, start_point: Point3D, end_point: Point3D) -> Option<RefinedPath> {
        if corridor_id >= self.corridors.len() {
            return None;
        }

        let corridor = &self.corridors[corridor_id];
        let gate_ids = &corridor.gates;

        if gate_ids.is_empty() {
            // 没有门，直接连接起点和终点
            let points = vec![start_point, end_point];
            let total_length = self.calculate_path_length(&points);
            return Some(RefinedPath {
                points,
                total_length,
                smoothness: 1.0,
            });
        }

        // 获取门的引用
        let gates: Vec<&CorridorGate> = gate_ids.iter()
            .filter_map(|&gate_id| self.corridor_gates.get(gate_id))
            .collect();

        if gates.is_empty() {
            return None;
        }

        // 初始化：每个门选择中心点作为初始通过点
        let mut gate_points: Vec<Point3D> = gates.iter()
            .map(|gate| self.calculate_gate_center(gate))
            .collect();

        // 迭代优化：最多10次迭代
        for _iteration in 0..10 {
            let mut improved = false;

            for i in 0..gate_points.len() {
                let current_cost = self.calculate_total_path_cost(&gate_points, &start_point, &end_point);

                // 在当前门的区域内尝试找更好的点
                if let Some(better_point) = self.find_better_point_in_gate(
                    gates[i],
                    &gate_points,
                    i,
                    &start_point,
                    &end_point
                ) {
                    let new_cost = {
                        let mut test_points = gate_points.clone();
                        test_points[i] = better_point.clone();
                        self.calculate_total_path_cost(&test_points, &start_point, &end_point)
                    };

                    if new_cost < current_cost {
                        gate_points[i] = better_point;
                        improved = true;
                    }
                }
            }

            if !improved {
                break; // 收敛了
            }
        }

        // 构建最终路径
        let mut final_points = vec![start_point];
        final_points.extend(gate_points);
        final_points.push(end_point);

        // 计算路径长度和平滑度
        let total_length = self.calculate_path_length(&final_points);
        let smoothness = self.calculate_path_smoothness(&final_points);

        Some(RefinedPath {
            points: final_points,
            total_length,
            smoothness,
        })
    }

    /// 计算门的中心点
    fn calculate_gate_center(&self, gate: &CorridorGate) -> Point3D {
        let shared_face = &gate.shared_face;
        // 使用共享面的中心
        Point3D::new(
            shared_face.bounds.x + shared_face.bounds.width / 2.0,
            shared_face.bounds.y + shared_face.bounds.height / 2.0,
            shared_face.bounds.z + shared_face.bounds.depth / 2.0,
        )
    }

    /// 计算路径总代价（距离 + 高度变化惩罚）
    fn calculate_total_path_cost(&self, gate_points: &[Point3D], start: &Point3D, end: &Point3D) -> f32 {
        let mut total_cost = 0.0;
        let mut current = start.clone();

        for point in gate_points {
            total_cost += current.distance_to(point);
            // 添加高度变化惩罚
            total_cost += self.height_change_penalty(&current, point);
            current = point.clone();
        }

        total_cost += current.distance_to(end);
        total_cost
    }

    /// 高度变化惩罚
    fn height_change_penalty(&self, from: &Point3D, to: &Point3D) -> f32 {
        let height_diff = (to.z - from.z).abs();
        height_diff * 0.5 // 高度变化的惩罚系数，可以调整
    }

    /// 在门区域内寻找更好的通过点
    fn find_better_point_in_gate(
        &self,
        gate: &CorridorGate,
        current_gate_points: &[Point3D],
        gate_index: usize,
        start: &Point3D,
        end: &Point3D,
    ) -> Option<Point3D> {
        let shared_face = &gate.shared_face;
        let bounds = &shared_face.bounds;

        // 在门的区域内尝试几个候选点
        let candidates = vec![
            // 门的中心
            Point3D::new(
                bounds.x + bounds.width / 2.0,
                bounds.y + bounds.height / 2.0,
                bounds.z + bounds.depth / 2.0,
            ),
            // 门的四个角的中点（在Z方向上）
            Point3D::new(bounds.x + bounds.width * 0.25, bounds.y + bounds.height * 0.25, bounds.z + bounds.depth / 2.0),
            Point3D::new(bounds.x + bounds.width * 0.75, bounds.y + bounds.height * 0.25, bounds.z + bounds.depth / 2.0),
            Point3D::new(bounds.x + bounds.width * 0.25, bounds.y + bounds.height * 0.75, bounds.z + bounds.depth / 2.0),
            Point3D::new(bounds.x + bounds.width * 0.75, bounds.y + bounds.height * 0.75, bounds.z + bounds.depth / 2.0),
            // 在Z方向上的不同高度
            Point3D::new(
                bounds.x + bounds.width / 2.0,
                bounds.y + bounds.height / 2.0,
                bounds.z + bounds.depth * 0.25,
            ),
            Point3D::new(
                bounds.x + bounds.width / 2.0,
                bounds.y + bounds.height / 2.0,
                bounds.z + bounds.depth * 0.75,
            ),
        ];

        let current_point = &current_gate_points[gate_index];
        let current_cost = self.calculate_point_cost_in_path(current_point, current_gate_points, gate_index, start, end);

        for candidate in candidates {
            // 确保候选点在门的边界内
            if self.point_in_box_bounds(&candidate, bounds) {
                let candidate_cost = self.calculate_point_cost_in_path(&candidate, current_gate_points, gate_index, start, end);
                if candidate_cost < current_cost {
                    return Some(candidate);
                }
            }
        }

        None
    }

    /// 检查点是否在3D边界框内
    fn point_in_box_bounds(&self, point: &Point3D, bounds: &Box3D) -> bool {
        point.x >= bounds.x && point.x <= bounds.x + bounds.width &&
            point.y >= bounds.y && point.y <= bounds.y + bounds.height &&
            point.z >= bounds.z && point.z <= bounds.z + bounds.depth
    }

    /// 计算某个点在路径中的代价贡献
    fn calculate_point_cost_in_path(
        &self,
        point: &Point3D,
        gate_points: &[Point3D],
        point_index: usize,
        start: &Point3D,
        end: &Point3D,
    ) -> f32 {
        let mut cost = 0.0;

        // 计算到前一个点的代价
        let prev_point = if point_index == 0 {
            start
        } else {
            &gate_points[point_index - 1]
        };
        cost += prev_point.distance_to(point);
        cost += self.height_change_penalty(prev_point, point);

        // 计算到下一个点的代价
        let next_point = if point_index == gate_points.len() - 1 {
            end
        } else {
            &gate_points[point_index + 1]
        };
        cost += point.distance_to(next_point);
        cost += self.height_change_penalty(point, next_point);

        cost
    }

    /// 保存体素数据到JSON文件
    pub fn save_voxel_data_to_file(&self, filename: &str) -> Result<(), Box<dyn std::error::Error>> {
        let voxel_data = self.export_voxel_data();
        let json_string = serde_json::to_string_pretty(&voxel_data)?;
        std::fs::write(filename, json_string)?;
        Ok(())
    }
}

impl MultiResolutionGrid {
    pub fn new(map_size: u32) -> Self {
        Self {
            cells: Vec::new(),
            map_size,
            obstacles: Vec::new(),
            adjacency: HashMap::new(),
        }
    }

    /// 添加矩形障碍物（兼容性方法）
    pub fn add_obstacle(&mut self, obstacle: Rectangle) {
        self.obstacles.push(Obstacle::Rectangle(obstacle));
    }

    /// 添加任意类型障碍物
    pub fn add_obstacle_any(&mut self, obstacle: Obstacle) {
        self.obstacles.push(obstacle);
    }

    /// 检查区域是否包含障碍物 - 使用精确的几何检测
    /// 对于1x1网格，只要有任何重叠就标记为不可通行
    fn check_obstacle_in_region(region: &Rectangle, obstacles: &[Obstacle]) -> NodeType {
        // 对于1x1的最小网格，采用保守策略：只要有重叠就不可通行
        if region.width <= 1.0 && region.height <= 1.0 {
            for obstacle in obstacles {
                if obstacle.overlaps_with_rectangle(region) {
                    return NodeType::Blocked;
                }
            }
            return NodeType::Empty;
        }

        // 对于较大的网格，检查重叠情况来决定是否需要细分
        let mut has_overlap = false;
        let region_area = region.width * region.height;
        let mut total_overlap_area = 0.0;

        for obstacle in obstacles {
            if obstacle.overlaps_with_rectangle(region) {
                has_overlap = true;
                let overlap_area = obstacle.overlap_with_rectangle(region);
                total_overlap_area += overlap_area;
            }
        }

        if !has_overlap {
            NodeType::Empty
        } else if total_overlap_area >= region_area * 0.99 {
            NodeType::Blocked  // 几乎完全被覆盖
        } else {
            NodeType::Mixed    // 部分覆盖，需要细分
        }
    }

    /// 计算障碍物与矩形的重叠面积（兼容性方法）
    pub fn calculate_overlap(rect1: &Rectangle, obstacle: &Obstacle) -> f32 {
        obstacle.overlap_with_rectangle(rect1)
    }

    /// 构建自适应网格 - 使用分层规则网格
    pub fn build_adaptive_grid(&mut self) {
        self.cells.clear();
        self.adjacency.clear();

        // 从最粗的网格开始，递归细化需要细化的区域
        let max_level = (self.map_size as f32).log2() as u8;

        // 从整个地图开始递归细化
        let root_bounds = Rectangle::new(0.0, 0.0, self.map_size as f32, self.map_size as f32);
        self.subdivide_region(root_bounds, 0, max_level);

        // 为每个单元分配ID
        for (i, cell) in self.cells.iter_mut().enumerate() {
            cell.id = i;
        }

        // 构建邻接关系
        self.build_adjacency();
    }

    /// 递归细分区域 - 确保网格边界对齐
    fn subdivide_region(&mut self, bounds: Rectangle, level: u8, max_level: u8) {
        // 检查当前区域的类型
        let node_type = Self::check_obstacle_in_region(&bounds, &self.obstacles);

        // 如果是纯空旷或纯障碍区域，或者已达到最大深度，或者已经是1x1大小，则停止细分
        if node_type != NodeType::Mixed || level >= max_level || bounds.width <= 1.0 || bounds.height <= 1.0 {
            let cell = GridCell {
                id: 0, // 临时ID，稍后会重新分配
                bounds,
                level,
                node_type,
            };
            self.cells.push(cell);
            return;
        }

        // 如果是混合区域，则细分为4个子区域
        // 确保细分后的边界仍然是整数坐标
        let current_size = bounds.width as u32;
        let half_size = current_size / 2;

        // 只有当当前大小是偶数时才能均匀细分
        if current_size % 2 != 0 || half_size == 0 {
            // 如果无法均匀细分，则停止细分
            let cell = GridCell {
                id: 0, // 临时ID，稍后会重新分配
                bounds,
                level,
                node_type,
            };
            self.cells.push(cell);
            return;
        }

        let x = bounds.x as u32;
        let y = bounds.y as u32;

        let children = [
            Rectangle::new(x as f32, y as f32, half_size as f32, half_size as f32),
            Rectangle::new((x + half_size) as f32, y as f32, half_size as f32, half_size as f32),
            Rectangle::new(x as f32, (y + half_size) as f32, half_size as f32, half_size as f32),
            Rectangle::new((x + half_size) as f32, (y + half_size) as f32, half_size as f32, half_size as f32),
        ];

        // 递归处理每个子区域
        for child_bounds in children.iter() {
            self.subdivide_region(*child_bounds, level + 1, max_level);
        }
    }

    /// 构建邻接关系
    fn build_adjacency(&mut self) {
        self.adjacency.clear();

        for i in 0..self.cells.len() {
            let mut neighbors = Vec::new();

            for j in 0..self.cells.len() {
                if i != j && self.are_adjacent(i, j) {
                    let cost = self.calculate_edge_cost(i, j);
                    neighbors.push((j, cost));
                }
            }

            self.adjacency.insert(i, neighbors);
        }
    }

    /// 检查两个网格单元是否相邻
    fn are_adjacent(&self, cell_a_id: usize, cell_b_id: usize) -> bool {
        let cell_a = &self.cells[cell_a_id];
        let cell_b = &self.cells[cell_b_id];

        // 只有空旷单元才能通行
        if cell_a.node_type == NodeType::Blocked || cell_b.node_type == NodeType::Blocked {
            return false;
        }

        // 检查两个矩形是否共享边界
        let a = &cell_a.bounds;
        let b = &cell_b.bounds;

        // 检查水平相邻
        let horizontal_adjacent =
            (a.x + a.width == b.x || b.x + b.width == a.x) &&
                !(a.y + a.height <= b.y || b.y + b.height <= a.y);

        // 检查垂直相邻
        let vertical_adjacent =
            (a.y + a.height == b.y || b.y + b.height == a.y) &&
                !(a.x + a.width <= b.x || b.x + b.width <= a.x);

        horizontal_adjacent || vertical_adjacent
    }

    /// 计算两个相邻网格单元之间的边代价
    fn calculate_edge_cost(&self, cell_a_id: usize, cell_b_id: usize) -> f32 {
        let cell_a = &self.cells[cell_a_id];
        let cell_b = &self.cells[cell_b_id];

        let center_a = cell_a.bounds.center();
        let center_b = cell_b.bounds.center();

        // 使用欧几里得距离
        let dx = center_a.0 - center_b.0;
        let dy = center_a.1 - center_b.1;
        (dx * dx + dy * dy).sqrt()
    }

    /// 找到包含指定点的网格单元
    pub fn find_cell_containing_point(&self, x: f32, y: f32) -> Option<usize> {
        for (i, cell) in self.cells.iter().enumerate() {
            if cell.bounds.contains_point(x, y) && cell.node_type != NodeType::Blocked {
                return Some(i);
            }
        }
        None
    }

    /// A*路径规划算法
    pub fn find_path(&self, start_x: f32, start_y: f32, goal_x: f32, goal_y: f32) -> Option<Vec<usize>> {
        // 找到起点和终点所在的网格单元
        let start_cell = self.find_cell_containing_point(start_x, start_y)?;
        let goal_cell = self.find_cell_containing_point(goal_x, goal_y)?;

        if start_cell == goal_cell {
            return Some(vec![start_cell]);
        }

        let mut open_set = BinaryHeap::new();
        let mut closed_set = HashSet::new();
        let mut came_from: HashMap<usize, usize> = HashMap::new();
        let mut g_score: HashMap<usize, f32> = HashMap::new();

        // 初始化起点
        g_score.insert(start_cell, 0.0);
        let h_cost = self.heuristic(start_cell, goal_cell);
        open_set.push(PathNode {
            cell_id: start_cell,
            g_cost: 0.0,
            h_cost,
            parent: None,
        });

        while let Some(current_node) = open_set.pop() {
            let current_cell = current_node.cell_id;

            if current_cell == goal_cell {
                // 重建路径
                return Some(self.reconstruct_path(&came_from, current_cell));
            }

            closed_set.insert(current_cell);

            // 检查所有邻居
            if let Some(neighbors) = self.adjacency.get(&current_cell) {
                for &(neighbor_cell, edge_cost) in neighbors {
                    if closed_set.contains(&neighbor_cell) {
                        continue;
                    }

                    let tentative_g = current_node.g_cost + edge_cost;

                    let is_better = if let Some(&existing_g) = g_score.get(&neighbor_cell) {
                        tentative_g < existing_g
                    } else {
                        true
                    };

                    if is_better {
                        came_from.insert(neighbor_cell, current_cell);
                        g_score.insert(neighbor_cell, tentative_g);

                        let h_cost = self.heuristic(neighbor_cell, goal_cell);
                        open_set.push(PathNode {
                            cell_id: neighbor_cell,
                            g_cost: tentative_g,
                            h_cost,
                            parent: Some(current_cell),
                        });
                    }
                }
            }
        }

        None // 没有找到路径
    }

    /// 启发式函数（欧几里得距离）
    fn heuristic(&self, cell_a: usize, cell_b: usize) -> f32 {
        let center_a = self.cells[cell_a].bounds.center();
        let center_b = self.cells[cell_b].bounds.center();

        let dx = center_a.0 - center_b.0;
        let dy = center_a.1 - center_b.1;
        (dx * dx + dy * dy).sqrt()
    }

    /// 重建路径
    fn reconstruct_path(&self, came_from: &HashMap<usize, usize>, mut current: usize) -> Vec<usize> {
        let mut path = vec![current];

        while let Some(&parent) = came_from.get(&current) {
            current = parent;
            path.push(current);
        }

        path.reverse();
        path
    }

    /// 动态添加障碍物并局部更新网格
    pub fn add_obstacle_dynamic(&mut self, new_obstacle: Rectangle) -> std::time::Duration {
        let start_time = std::time::Instant::now();

        // 1. 添加新障碍物
        let obstacle = Obstacle::Rectangle(new_obstacle);
        self.obstacles.push(obstacle.clone());

        // 2. 找到受影响的网格单元
        let mut affected_cells = Vec::new();
        for (i, cell) in self.cells.iter().enumerate() {
            let overlap = Self::calculate_overlap(&cell.bounds, &obstacle);
            if overlap > 0.0 {
                affected_cells.push(i);
            }
        }

        println!("新障碍物影响了 {} 个网格单元", affected_cells.len());

        // 3. 更新受影响单元的类型
        for &cell_id in &affected_cells {
            let cell = &mut self.cells[cell_id];
            cell.node_type = Self::check_obstacle_in_region(&cell.bounds, &self.obstacles);
        }

        // 4. 检查是否需要细分受影响的单元
        let mut cells_to_subdivide = Vec::new();
        for &cell_id in &affected_cells {
            let cell = &self.cells[cell_id];
            if cell.node_type == NodeType::Mixed && cell.bounds.width > 1.0 {
                cells_to_subdivide.push(cell_id);
            }
        }

        // 5. 细分混合类型的单元
        if !cells_to_subdivide.is_empty() {
            println!("需要细分 {} 个混合单元", cells_to_subdivide.len());

            // 从后往前删除，避免索引问题
            cells_to_subdivide.sort_by(|a, b| b.cmp(a));

            for &cell_id in &cells_to_subdivide {
                let cell = self.cells.remove(cell_id);
                self.subdivide_cell_locally(cell);
            }
        }

        // 6. 重新分配ID
        for (i, cell) in self.cells.iter_mut().enumerate() {
            cell.id = i;
        }

        // 7. 局部重建邻接关系（只重建受影响的部分）
        self.rebuild_local_adjacency(&affected_cells);

        start_time.elapsed()
    }

    /// 局部细分单个网格单元
    fn subdivide_cell_locally(&mut self, cell: GridCell) {
        let max_level = (self.map_size as f32).log2() as u8;

        // 如果已经是最小单元或者不是混合类型，直接添加回去
        if cell.bounds.width <= 1.0 || cell.node_type != NodeType::Mixed {
            self.cells.push(cell);
            return;
        }

        // 细分为4个子单元
        let current_size = cell.bounds.width as u32;
        let half_size = current_size / 2;

        if current_size % 2 != 0 || half_size == 0 {
            self.cells.push(cell);
            return;
        }

        let x = cell.bounds.x as u32;
        let y = cell.bounds.y as u32;

        let children_bounds = [
            Rectangle::new(x as f32, y as f32, half_size as f32, half_size as f32),
            Rectangle::new((x + half_size) as f32, y as f32, half_size as f32, half_size as f32),
            Rectangle::new(x as f32, (y + half_size) as f32, half_size as f32, half_size as f32),
            Rectangle::new((x + half_size) as f32, (y + half_size) as f32, half_size as f32, half_size as f32),
        ];

        for child_bounds in children_bounds.iter() {
            let node_type = Self::check_obstacle_in_region(child_bounds, &self.obstacles);
            let child_cell = GridCell {
                id: 0, // 临时ID
                bounds: *child_bounds,
                level: cell.level + 1,
                node_type,
            };

            // 如果子单元仍然是混合类型且可以继续细分，递归细分
            if node_type == NodeType::Mixed && child_bounds.width > 1.0 && cell.level + 1 < max_level {
                self.subdivide_cell_locally(child_cell);
            } else {
                self.cells.push(child_cell);
            }
        }
    }

    /// 局部重建邻接关系
    fn rebuild_local_adjacency(&mut self, _affected_cell_ids: &[usize]) {
        // 简化版本：重建所有邻接关系
        // 在实际应用中，可以只重建受影响单元及其邻居的关系
        self.build_adjacency();
    }
}

/// 可视化模块
pub struct GridVisualizer {
    pub image_size: u32,
    pub scale: f32,
}

impl GridVisualizer {
    pub fn new(image_size: u32, map_size: f32) -> Self {
        Self {
            image_size,
            scale: image_size as f32 / map_size,
        }
    }

    /// 将世界坐标转换为图像坐标
    fn world_to_image(&self, x: f32, y: f32) -> (i32, i32) {
        let img_x = (x * self.scale) as i32;
        let img_y = (y * self.scale) as i32;
        (img_x, img_y)
    }

    /// 绘制网格可视化图像
    pub fn visualize_grid(&self, grid: &MultiResolutionGrid) -> RgbImage {
        self.visualize_grid_with_path(grid, None, None, None)
    }

    /// 绘制带路径的网格可视化图像
    pub fn visualize_grid_with_path(
        &self,
        grid: &MultiResolutionGrid,
        path: Option<&[usize]>,
        start_point: Option<(f32, f32)>,
        goal_point: Option<(f32, f32)>
    ) -> RgbImage {
        let mut image = ImageBuffer::new(self.image_size, self.image_size);

        // 填充白色背景
        for pixel in image.pixels_mut() {
            *pixel = Rgb([255, 255, 255]);
        }

        // 绘制1x1背景网格（非常淡的灰色边框）
        // 对于大地图，使用更淡的颜色和稀疏采样
        let grid_color = if grid.map_size > 64 { [240, 240, 240] } else { [220, 220, 220] };
        let step = if grid.map_size > 64 { 4 } else { 1 }; // 大地图时每4个单位画一条线

        for y in (0..grid.map_size).step_by(step as usize) {
            for x in (0..grid.map_size).step_by(step as usize) {
                let cell_bounds = Rectangle::new(x as f32, y as f32, step as f32, step as f32);
                self.draw_rectangle_border(&mut image, &cell_bounds, grid_color);
            }
        }

        // 绘制障碍物（黑色填充）
        for obstacle in &grid.obstacles {
            self.draw_obstacle(&mut image, obstacle, [0, 0, 0]);
        }

        // 绘制所有网格单元的边框（较粗的边框）
        for cell in &grid.cells {
            let color = match cell.node_type {
                NodeType::Empty => [0, 255, 0],    // 绿色边框 - 空旷区域
                NodeType::Blocked => [255, 0, 0],  // 红色边框 - 障碍区域
                NodeType::Mixed => [0, 0, 255],    // 蓝色边框 - 混合区域
            };

            // 绘制较粗的边框
            self.draw_thick_rectangle_border(&mut image, &cell.bounds, color);
        }

        // 绘制完整路径（包括起点到第一个网格、最后一个网格到终点的连接）
        if let (Some(path_cells), Some(start), Some(goal)) = (path, start_point, goal_point) {
            self.draw_complete_path(&mut image, grid, path_cells, start, goal);
        }

        // 绘制起点和终点
        if let Some((start_x, start_y)) = start_point {
            self.draw_point(&mut image, start_x, start_y, [255, 255, 0], 6); // 黄色起点
        }

        if let Some((goal_x, goal_y)) = goal_point {
            self.draw_point(&mut image, goal_x, goal_y, [255, 0, 255], 6); // 紫色终点
        }

        image
    }

    /// 绘制填充矩形
    fn draw_filled_rectangle(&self, image: &mut RgbImage, rect: &Rectangle, color: [u8; 3]) {
        let (x1, y1) = self.world_to_image(rect.x, rect.y);
        let (x2, y2) = self.world_to_image(rect.x + rect.width, rect.y + rect.height);

        let width = (x2 - x1).max(1) as u32;
        let height = (y2 - y1).max(1) as u32;

        if x1 >= 0 && y1 >= 0 && x1 < self.image_size as i32 && y1 < self.image_size as i32 {
            let rect = Rect::at(x1, y1).of_size(width, height);
            draw_filled_rect_mut(image, rect, Rgb(color));
        }
    }

    /// 绘制矩形边框
    fn draw_rectangle_border(&self, image: &mut RgbImage, rect: &Rectangle, color: [u8; 3]) {
        let (x1, y1) = self.world_to_image(rect.x, rect.y);
        let (x2, y2) = self.world_to_image(rect.x + rect.width, rect.y + rect.height);

        let width = (x2 - x1).max(1) as u32;
        let height = (y2 - y1).max(1) as u32;

        if x1 >= 0 && y1 >= 0 && x1 < self.image_size as i32 && y1 < self.image_size as i32 {
            let rect = Rect::at(x1, y1).of_size(width, height);
            draw_hollow_rect_mut(image, rect, Rgb(color));
        }
    }

    /// 绘制较粗的矩形边框
    fn draw_thick_rectangle_border(&self, image: &mut RgbImage, rect: &Rectangle, color: [u8; 3]) {
        let (x1, y1) = self.world_to_image(rect.x, rect.y);
        let (x2, y2) = self.world_to_image(rect.x + rect.width, rect.y + rect.height);

        let width = (x2 - x1).max(1) as u32;
        let height = (y2 - y1).max(1) as u32;

        if x1 >= 0 && y1 >= 0 && x1 < self.image_size as i32 && y1 < self.image_size as i32 {
            let rect = Rect::at(x1, y1).of_size(width, height);
            // 绘制多层边框来实现粗边框效果
            draw_hollow_rect_mut(image, rect, Rgb(color));
            if width > 2 && height > 2 {
                let inner_rect = Rect::at(x1 + 1, y1 + 1).of_size(width - 2, height - 2);
                draw_hollow_rect_mut(image, inner_rect, Rgb(color));
            }
        }
    }

    /// 绘制路径
    fn draw_path(&self, image: &mut RgbImage, grid: &MultiResolutionGrid, path: &[usize]) {
        if path.is_empty() {
            return;
        }

        // 如果只有一个网格单元，不需要绘制路径
        if path.len() == 1 {
            return;
        }

        // 绘制网格中心之间的连接
        for i in 0..path.len() - 1 {
            let current_cell = &grid.cells[path[i]];
            let next_cell = &grid.cells[path[i + 1]];

            let current_center = current_cell.bounds.center();
            let next_center = next_cell.bounds.center();

            self.draw_thick_line(image, current_center, next_center, [255, 165, 0]); // 橙色路径
        }
    }

    /// 绘制完整路径（包括起点到第一个网格中心，最后一个网格中心到终点）
    fn draw_complete_path(
        &self,
        image: &mut RgbImage,
        grid: &MultiResolutionGrid,
        path: &[usize],
        start_point: (f32, f32),
        goal_point: (f32, f32)
    ) {
        if path.is_empty() {
            return;
        }

        if path.len() == 1 {
            // 如果起点和终点在同一个网格中，直接连接
            self.draw_thick_line(image, start_point, goal_point, [255, 165, 0]);
            return;
        }

        // 1. 绘制起点到第一个网格中心的连接
        let first_cell = &grid.cells[path[0]];
        let first_center = first_cell.bounds.center();
        self.draw_thick_line(image, start_point, first_center, [255, 165, 0]);

        // 2. 绘制网格中心之间的连接
        for i in 0..path.len() - 1 {
            let current_cell = &grid.cells[path[i]];
            let next_cell = &grid.cells[path[i + 1]];

            let current_center = current_cell.bounds.center();
            let next_center = next_cell.bounds.center();

            self.draw_thick_line(image, current_center, next_center, [255, 165, 0]);
        }

        // 3. 绘制最后一个网格中心到终点的连接
        let last_cell = &grid.cells[path[path.len() - 1]];
        let last_center = last_cell.bounds.center();
        self.draw_thick_line(image, last_center, goal_point, [255, 165, 0]);
    }

    /// 绘制粗线条
    fn draw_thick_line(&self, image: &mut RgbImage, start: (f32, f32), end: (f32, f32), color: [u8; 3]) {
        let (x1, y1) = self.world_to_image(start.0, start.1);
        let (x2, y2) = self.world_to_image(end.0, end.1);

        // 绘制主线条
        draw_line_segment_mut(image, (x1 as f32, y1 as f32), (x2 as f32, y2 as f32), Rgb(color));

        // 绘制更粗的路径（通过绘制多条相邻的线）
        for dx in -1..=1 {
            for dy in -1..=1 {
                if dx != 0 || dy != 0 {
                    draw_line_segment_mut(
                        image,
                        ((x1 + dx) as f32, (y1 + dy) as f32),
                        ((x2 + dx) as f32, (y2 + dy) as f32),
                        Rgb(color)
                    );
                }
            }
        }
    }

    /// 绘制点（起点或终点）
    fn draw_point(&self, image: &mut RgbImage, x: f32, y: f32, color: [u8; 3], radius: i32) {
        let (img_x, img_y) = self.world_to_image(x, y);

        // 绘制实心圆
        for dy in -radius..=radius {
            for dx in -radius..=radius {
                if dx * dx + dy * dy <= radius * radius {
                    let px = img_x + dx;
                    let py = img_y + dy;

                    if px >= 0 && py >= 0 && px < self.image_size as i32 && py < self.image_size as i32 {
                        image.put_pixel(px as u32, py as u32, Rgb(color));
                    }
                }
            }
        }
    }

    /// 绘制障碍物（支持不同类型）
    fn draw_obstacle(&self, image: &mut RgbImage, obstacle: &Obstacle, color: [u8; 3]) {
        match obstacle {
            Obstacle::Rectangle(rect) => {
                self.draw_filled_rectangle(image, rect, color);
            }
            Obstacle::Circle { center, radius } => {
                self.draw_filled_circle(image, center, *radius, color);
            }
            Obstacle::Polygon { vertices } => {
                self.draw_filled_polygon(image, vertices, color);
            }
            Obstacle::PointSet { points, radius } => {
                for point in points {
                    self.draw_filled_circle(image, point, *radius, color);
                }
            }
        }
    }

    /// 绘制填充圆形
    fn draw_filled_circle(&self, image: &mut RgbImage, center: &Point, radius: f32, color: [u8; 3]) {
        let (center_x, center_y) = self.world_to_image(center.x, center.y);
        let img_radius = (radius * self.scale) as i32;

        for dy in -img_radius..=img_radius {
            for dx in -img_radius..=img_radius {
                if dx * dx + dy * dy <= img_radius * img_radius {
                    let px = center_x + dx;
                    let py = center_y + dy;

                    if px >= 0 && py >= 0 && px < self.image_size as i32 && py < self.image_size as i32 {
                        image.put_pixel(px as u32, py as u32, Rgb(color));
                    }
                }
            }
        }
    }

    /// 绘制填充多边形（简化版本）
    fn draw_filled_polygon(&self, image: &mut RgbImage, vertices: &[Point], color: [u8; 3]) {
        if vertices.len() < 3 {
            return;
        }

        // 找到边界框
        let min_x = vertices.iter().map(|p| p.x).fold(f32::INFINITY, f32::min);
        let max_x = vertices.iter().map(|p| p.x).fold(f32::NEG_INFINITY, f32::max);
        let min_y = vertices.iter().map(|p| p.y).fold(f32::INFINITY, f32::min);
        let max_y = vertices.iter().map(|p| p.y).fold(f32::NEG_INFINITY, f32::max);

        let (start_x, start_y) = self.world_to_image(min_x, min_y);
        let (end_x, end_y) = self.world_to_image(max_x, max_y);

        // 扫描填充
        for py in start_y..=end_y {
            for px in start_x..=end_x {
                if px >= 0 && py >= 0 && px < self.image_size as i32 && py < self.image_size as i32 {
                    let world_x = px as f32 / self.scale;
                    let world_y = py as f32 / self.scale;

                    if Obstacle::point_in_polygon(world_x, world_y, vertices) {
                        image.put_pixel(px as u32, py as u32, Rgb(color));
                    }
                }
            }
        }
    }
}

fn main() {
    println!("开始创建128x128x32三维多分辨率网格测试...");

    // 创建128x128x32的3D网格系统
    let mut grid_3d = MultiResolutionGrid3D::new(128, 32);

    // 添加3D障碍物到128x128x32地图上
    println!("添加3D障碍物到128x128x32地图:");

    // 1. 长方体障碍物（建筑物）- 添加多个
    let box_obstacles = vec![
        Box3D::new(20.0, 15.0, 0.0, 8.0, 6.0, 20.0),    // 大建筑
        Box3D::new(45.0, 10.0, 0.0, 4.0, 4.0, 25.0),    // 高楼
        Box3D::new(80.0, 8.0, 0.0, 6.0, 8.0, 15.0),     // 中等建筑
        Box3D::new(110.0, 20.0, 0.0, 5.0, 5.0, 18.0),   // 小建筑
        Box3D::new(15.0, 40.0, 0.0, 3.0, 10.0, 30.0),   // 狭长高楼
        Box3D::new(35.0, 35.0, 0.0, 8.0, 8.0, 12.0),    // 方形建筑
        Box3D::new(60.0, 30.0, 0.0, 4.0, 6.0, 28.0),    // 超高楼
        Box3D::new(85.0, 40.0, 0.0, 6.0, 4.0, 10.0),    // 低矮建筑
    ];

    for (i, obstacle) in box_obstacles.iter().enumerate() {
        grid_3d.add_obstacle(Obstacle3D::Box(*obstacle));
        println!("  长方体障碍物{}: x={:.0}, y={:.0}, z={:.0}, 尺寸={}x{}x{}",
                 i + 1, obstacle.x, obstacle.y, obstacle.z,
                 obstacle.width, obstacle.height, obstacle.depth);
    }

    // 2. 圆柱体障碍物（塔楼）- 添加多个
    let cylinder_obstacles = vec![
        (Point3D::new(30.0, 80.0, 0.0), 8.0, 25.0),   // 大塔楼
        (Point3D::new(90.0, 15.0, 0.0), 3.0, 32.0),   // 细高塔
        (Point3D::new(70.0, 100.0, 0.0), 5.0, 20.0),  // 中等塔楼
        (Point3D::new(25.0, 70.0, 0.0), 4.0, 22.0),   // 小塔楼
        (Point3D::new(105.0, 45.0, 0.0), 6.0, 18.0),  // 粗塔楼
    ];

    for (i, (center, radius, height)) in cylinder_obstacles.iter().enumerate() {
        grid_3d.add_obstacle(Obstacle3D::Cylinder {
            center: *center,
            radius: *radius,
            height: *height
        });
        println!("  圆柱体障碍物{}: 中心({}, {}, {}), 半径={}, 高度={}",
                 i + 1, center.x, center.y, center.z, radius, height);
    }

    // 3. 棱柱障碍物（不规则建筑）- 添加多个
    let prism_obstacles = vec![
        // 三角形棱柱
        (vec![
            Point::new(100.0, 25.0),
            Point::new(110.0, 35.0),
            Point::new(90.0, 35.0),
        ], 0.0, 24.0),
        // 五边形棱柱
        (vec![
            Point::new(50.0, 65.0),
            Point::new(58.0, 65.0),
            Point::new(60.0, 70.0),
            Point::new(55.0, 75.0),
            Point::new(48.0, 70.0),
        ], 0.0, 16.0),
        // L形棱柱
        (vec![
            Point::new(75.0, 70.0),
            Point::new(85.0, 70.0),
            Point::new(85.0, 75.0),
            Point::new(80.0, 75.0),
            Point::new(80.0, 85.0),
            Point::new(75.0, 85.0),
        ], 0.0, 20.0),
    ];

    for (i, (vertices, bottom, height)) in prism_obstacles.iter().enumerate() {
        grid_3d.add_obstacle(Obstacle3D::Prism {
            vertices: vertices.clone(),
            bottom: *bottom,
            height: *height
        });
        println!("  棱柱障碍物{}: {}个顶点, 底部高度={}, 高度={}",
                 i + 1, vertices.len(), bottom, height);
    }

    // 4. 3D点集障碍物（山体、岩石群）- 添加多个点集
    let pointset_obstacles = vec![
        // 山体群1
        (vec![
            Point3D::new(65.0, 15.0, 8.0),
            Point3D::new(68.0, 18.0, 12.0),
            Point3D::new(62.0, 20.0, 6.0),
            Point3D::new(70.0, 22.0, 15.0),
        ], 3.0),
        // 岩石群2
        (vec![
            Point3D::new(115.0, 85.0, 5.0),
            Point3D::new(118.0, 87.0, 8.0),
            Point3D::new(112.0, 90.0, 4.0),
            Point3D::new(120.0, 88.0, 10.0),
            Point3D::new(116.0, 92.0, 7.0),
        ], 2.5),
        // 小山丘3
        (vec![
            Point3D::new(40.0, 95.0, 3.0),
            Point3D::new(42.0, 98.0, 6.0),
            Point3D::new(38.0, 100.0, 2.0),
        ], 4.0),
    ];

    for (i, (points, radius)) in pointset_obstacles.iter().enumerate() {
        grid_3d.add_obstacle(Obstacle3D::PointSet {
            points: points.clone(),
            radius: *radius
        });
        println!("  3D点集障碍物{}: {}个点，每个半径{}",
                 i + 1, points.len(), radius);
    }

    println!("地图大小: {}x{}x{}", grid_3d.map_size, grid_3d.map_size, grid_3d.map_height);
    println!("最小网格大小: 1x1x1 单位");

    // 构建3D自适应网格
    println!("\n构建3D自适应网格...");
    let grid_start_time = Instant::now();
    grid_3d.build_adaptive_grid();
    let grid_duration = grid_start_time.elapsed();
    println!("3D网格构建完成，耗时: {:.2?}", grid_duration);

    // 调试：检查网格构建结果
    println!("调试 - 3D网格构建结果:");
    println!("  总网格单元数: {}", grid_3d.cells.len());
    if grid_3d.cells.len() > 0 {
        println!("  前5个网格单元:");
        for (i, cell) in grid_3d.cells.iter().take(5).enumerate() {
            println!("    单元{}: 位置({:.0},{:.0},{:.0}) 大小({:.0}x{:.0}x{:.0}) 层级={} 类型={:?}",
                     i, cell.bounds.x, cell.bounds.y, cell.bounds.z,
                     cell.bounds.width, cell.bounds.height, cell.bounds.depth,
                     cell.level, cell.node_type);
        }
    } else {
        println!("  警告：没有生成任何网格单元！");
    }

    // 调试：检查所有障碍物区域的网格
    println!("调试信息:");

    // 检查与任意障碍物重叠的网格单元
    let mut obstacle_cells = 0;
    for cell in &grid_3d.cells {
        let mut total_overlap = 0.0;
        for obstacle in &grid_3d.obstacles {
            let overlap_volume = obstacle.overlap_with_box(&cell.bounds);
            total_overlap += overlap_volume;
        }

        if total_overlap > 0.0 {
            obstacle_cells += 1;
            if obstacle_cells <= 10 { // 只显示前10个，避免输出过多
                println!("与障碍物重叠的单元: ({}, {}, {}, {}x{}x{}) 类型={:?} 重叠体积={:.1}",
                         cell.bounds.x, cell.bounds.y, cell.bounds.z,
                         cell.bounds.width, cell.bounds.height, cell.bounds.depth,
                         cell.node_type, total_overlap);
            }
        }
    }
    println!("与障碍物重叠的网格单元总数: {}", obstacle_cells);

    // 3D路径规划测试
    println!("\n开始3D路径规划测试...");
    let start_point = (10.0, 10.0, 5.0);      // 起点：左下角空旷区域
    let goal_point = (115.0, 115.0, 15.0);    // 终点：右上角空旷区域

    println!("起点: ({}, {}, {})", start_point.0, start_point.1, start_point.2);
    println!("终点: ({}, {}, {})", goal_point.0, goal_point.1, goal_point.2);

    // 路径规划计时
    let pathfinding_start_time = Instant::now();
    let path = grid_3d.find_path(start_point.0, start_point.1, start_point.2,
                                 goal_point.0, goal_point.1, goal_point.2);
    let pathfinding_duration = pathfinding_start_time.elapsed();

    println!("3D路径规划完成，耗时: {:.2?}", pathfinding_duration);

    match &path {
        Some(path_cells) => {
            println!("找到3D路径！路径长度: {} 个网格单元", path_cells.len());
            println!("路径经过的网格单元:");
            for (i, &cell_id) in path_cells.iter().enumerate() {
                let cell = &grid_3d.cells[cell_id];
                let center = cell.bounds.center();
                println!("  步骤 {}: 单元{} 中心({:.1}, {:.1}, {:.1}) 大小({}x{}x{}) 层级={}",
                         i + 1, cell_id,
                         center.0, center.1, center.2,
                         cell.bounds.width, cell.bounds.height, cell.bounds.depth,
                         cell.level);

                // 显示邻居信息
                if let Some(neighbors) = grid_3d.adjacency.get(&cell_id) {
                    println!("    邻居数量: {}", neighbors.len());
                    if neighbors.len() < 10 { // 只显示少量邻居，避免输出过多
                        for (neighbor_id, cost) in neighbors {
                            let neighbor_cell = &grid_3d.cells[*neighbor_id];
                            let neighbor_center = neighbor_cell.bounds.center();
                            println!("      -> 单元{} 中心({:.1}, {:.1}, {:.1}) 大小({}x{}x{}) 代价={:.2}",
                                     neighbor_id, neighbor_center.0, neighbor_center.1, neighbor_center.2,
                                     neighbor_cell.bounds.width, neighbor_cell.bounds.height, neighbor_cell.bounds.depth, cost);

                            // 如果代价异常大，显示详细边界信息
                            if *cost > 3.0 {
                                println!("        异常邻接! 当前网格边界: ({:.1},{:.1},{:.1}) 到 ({:.1},{:.1},{:.1})",
                                         cell.bounds.x, cell.bounds.y, cell.bounds.z,
                                         cell.bounds.x + cell.bounds.width,
                                         cell.bounds.y + cell.bounds.height,
                                         cell.bounds.z + cell.bounds.depth);
                                println!("        邻居网格边界: ({:.1},{:.1},{:.1}) 到 ({:.1},{:.1},{:.1})",
                                         neighbor_cell.bounds.x, neighbor_cell.bounds.y, neighbor_cell.bounds.z,
                                         neighbor_cell.bounds.x + neighbor_cell.bounds.width,
                                         neighbor_cell.bounds.y + neighbor_cell.bounds.height,
                                         neighbor_cell.bounds.z + neighbor_cell.bounds.depth);
                            }
                        }
                    }
                }
            }
        }
        None => {
            println!("未找到3D路径！");
        }
    }

    // 统计3D网格单元信息
    let mut empty_count = 0;
    let mut blocked_count = 0;
    let mut mixed_count = 0;
    let mut level_counts = HashMap::new();

    for cell in &grid_3d.cells {
        match cell.node_type {
            NodeType::Empty => empty_count += 1,
            NodeType::Blocked => blocked_count += 1,
            NodeType::Mixed => mixed_count += 1,
        }

        *level_counts.entry(cell.level).or_insert(0) += 1;
    }

    println!("3D网格统计信息:");
    println!("  总网格单元数: {}", grid_3d.cells.len());
    println!("  空旷单元: {}", empty_count);
    println!("  障碍单元: {}", blocked_count);
    println!("  混合单元: {}", mixed_count);

    println!("  各层级单元数:");
    for (level, count) in level_counts.iter() {
        // 层级越高，网格单元越小。从整个地图大小开始，每次细分时大小减半
        let cell_size = grid_3d.map_size / (2_u32.pow(*level as u32));
        println!("    层级{} ({}x{}x{}单元): {} 个", level, cell_size, cell_size, cell_size, count);
    }

    // 导出3D体素数据用于Python可视化
    println!("\n导出3D体素数据...");
    let export_start_time = Instant::now();

    // 获取体素数据并添加路径点
    let mut voxel_data = grid_3d.export_voxel_data();

    // 如果找到了路径，将路径点添加到导出数据中，并创建管道
    if let Some(ref path_cells) = path {
        let mut path_points = Vec::new();
        for &cell_id in path_cells {
            let cell = &grid_3d.cells[cell_id];
            let center = cell.bounds.center();
            path_points.push(Point3D::new(center.0, center.1, center.2));
        }
        voxel_data.path_points = Some(path_points.clone());
        println!("路径点已添加到导出数据: {} 个点", path_cells.len());

        // 创建管道
        let corridor_start_time = Instant::now();
        let corridor_start_point = Point3D::new(start_point.0, start_point.1, start_point.2);
        let corridor_end_point = Point3D::new(goal_point.0, goal_point.1, goal_point.2);
        if let Some(corridor_id) = grid_3d.create_corridor_from_path(path_cells, corridor_start_point.clone(), corridor_end_point.clone()) {
            println!("创建了管道 ID: {}", corridor_id);
            println!("管道包含 {} 个门", grid_3d.corridors[corridor_id].gates.len());
            println!("管道总长度: {:.2}", grid_3d.corridors[corridor_id].total_length);
            println!("最小截面积: {:.2}", grid_3d.corridors[corridor_id].min_cross_section);

            // 应用智能路径细化（优先使用门优化）
            println!("\n应用智能路径细化...");
            let refine_start_time = Instant::now();
            if let Some(refined_path) = grid_3d.refine_path_intelligently(path_cells, corridor_start_point, corridor_end_point) {
                let refine_duration = refine_start_time.elapsed();
                println!("虚拟细网格路径细化完成，耗时: {:.2?}", refine_duration);
                println!("原始路径点数: {}", path_cells.len());
                println!("细化路径点数: {}", refined_path.points.len());
                println!("细化路径长度: {:.2}", refined_path.total_length);
                println!("路径平滑度: {:.3}", refined_path.smoothness);

                // 重新导出数据以包含细化路径信息
                voxel_data = grid_3d.export_voxel_data();
                voxel_data.path_points = Some(path_points.clone());
                voxel_data.refined_path = Some(refined_path);
            } else {
                println!("虚拟细网格路径细化失败");
                // 重新导出数据
                voxel_data = grid_3d.export_voxel_data();
                voxel_data.path_points = Some(path_points);
            }
        } else {
            // 重新导出数据
            voxel_data = grid_3d.export_voxel_data();
            voxel_data.path_points = Some(path_points);
        }

        let corridor_duration = corridor_start_time.elapsed();

        println!("路径细化完成，耗时: {:.2?}", corridor_duration);
    }

    // 保存到文件
    let json_string = serde_json::to_string_pretty(&voxel_data).unwrap();
    match std::fs::write("voxel_data_3d.json", json_string) {
        Ok(_) => {
            let export_duration = export_start_time.elapsed();
            println!("3D体素数据已导出到 voxel_data_3d.json，耗时: {:.2?}", export_duration);
            println!("请使用Python脚本进行3D可视化");
        }
        Err(e) => {
            println!("导出体素数据失败: {}", e);
        }
    }

    // 打印一些3D网格单元的详细信息
    println!("\n前10个3D网格单元的详细信息:");
    for (i, cell) in grid_3d.cells.iter().take(10).enumerate() {
        println!("单元 {}: 层级={}, 类型={:?}, 边界=({:.0}, {:.0}, {:.0}, {}x{}x{})",
                 i + 1,
                 cell.level,
                 cell.node_type,
                 cell.bounds.x,
                 cell.bounds.y,
                 cell.bounds.z,
                 cell.bounds.width,
                 cell.bounds.height,
                 cell.bounds.depth);
    }

    println!("\n=== 3D性能统计 ===");
    println!("地图大小: {}x{}x32", grid_3d.map_size, grid_3d.map_size);
    println!("障碍物数量: {}", grid_3d.obstacles.len());
    println!("网格单元总数: {}", grid_3d.cells.len());
    println!("网格构建时间: {:.2?}", grid_duration);
    println!("路径规划时间: {:.2?}", pathfinding_duration);

    let total_time = grid_duration + pathfinding_duration;
    println!("总耗时: {:.2?}", total_time);

    // 计算3D网格压缩率（注意Z轴只有32层）
    let total_possible_cells = (grid_3d.map_size * grid_3d.map_size * 32) as f32;
    let compression_ratio = (total_possible_cells - grid_3d.cells.len() as f32) / total_possible_cells * 100.0;
    println!("3D网格压缩率: {:.1}% (从{}个1x1x1网格压缩到{}个多分辨率网格)",
             compression_ratio, total_possible_cells as u32, grid_3d.cells.len());

    println!("\n=== 3D测试完成 ===");
    println!("3D多分辨率网格系统测试完成！");
    println!("数据已导出到 voxel_data_3d.json");
    println!("\n请使用以下Python脚本进行3D可视化:");
    println!("python visualize_3d_slice.py");
    println!("\n可视化说明:");
    println!("  - 绿色体素: 空旷区域的网格单元");
    println!("  - 红色体素: 被障碍物占据的网格单元");
    println!("  - 黑色体素: 障碍物本身");
    println!("  - 网格边框: 显示多分辨率网格的边界，用于验证网格划分准确性");
}
