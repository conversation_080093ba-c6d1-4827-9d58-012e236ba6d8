#!/usr/bin/env python3
"""
Rust漏斗算法结果可视化
显示原始A*路径、门户和Rust实现的漏斗算法细化路径
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection


def load_voxel_data(filename):
    """加载体素数据"""
    with open(filename, "r") as f:
        data = json.load(f)
    return data


def create_gate_rectangle(vertices, scale=0.8):
    """创建门户矩形"""
    if len(vertices) >= 4:
        # 计算中心点
        center_x = sum(v["x"] for v in vertices) / len(vertices)
        center_y = sum(v["y"] for v in vertices) / len(vertices)
        center_z = sum(v["z"] for v in vertices) / len(vertices)
        
        # 缩放顶点
        scaled_vertices = []
        for v in vertices:
            scaled_x = center_x + (v["x"] - center_x) * scale
            scaled_y = center_y + (v["y"] - center_y) * scale
            scaled_z = center_z + (v["z"] - center_z) * scale
            scaled_vertices.append([scaled_x, scaled_y, scaled_z])
        
        return scaled_vertices
    return None


def extract_gates_from_corridor(voxel_data):
    """从管道数据中提取门户信息"""
    if "corridors" not in voxel_data or not voxel_data["corridors"]:
        return None, None, None
    
    corridor = voxel_data["corridors"][0]
    corridor_gates = voxel_data.get("corridor_gates", [])
    
    gates = []
    gate_ids = corridor["gates"]
    
    for gate_id in gate_ids:
        if gate_id < len(corridor_gates):
            gate = corridor_gates[gate_id]
            shared_face = gate["shared_face"]
            vertices = shared_face["vertices"]
            
            gates.append({
                'vertices': vertices,
                'center': gate["center_point"]
            })
    
    start_point = corridor["start_point"]
    end_point = corridor["end_point"]
    
    start = [start_point["x"], start_point["y"], start_point["z"]]
    end = [end_point["x"], end_point["y"], end_point["z"]]
    
    return gates, start, end


def visualize_rust_funnel_result(voxel_data):
    """可视化Rust漏斗算法结果"""
    fig = plt.figure(figsize=(15, 10))
    ax = fig.add_subplot(111, projection="3d")
    
    size_xy = voxel_data["size_xy"]
    size_z = voxel_data["size_z"]
    
    print(f"地图大小: {size_xy}x{size_xy}x{size_z}")
    
    # 提取门户和路径信息
    gates, start_point, end_point = extract_gates_from_corridor(voxel_data)
    
    if not gates:
        print("没有找到门户数据")
        return None
    
    print(f"找到 {len(gates)} 个门户")
    
    # 1. 绘制门户（浅蓝色矩形）
    gate_rectangles = []
    for gate in gates:
        rect_vertices = create_gate_rectangle(gate['vertices'])
        if rect_vertices:
            gate_rectangles.append(rect_vertices)
    
    if gate_rectangles:
        gate_collection = Poly3DCollection(
            gate_rectangles,
            facecolors="lightblue",
            edgecolors="blue",
            alpha=0.6,
            linewidths=1,
        )
        ax.add_collection3d(gate_collection)
    
    # 2. 绘制原始A*路径（红色虚线）
    original_path_shown = False
    if "path_points" in voxel_data and voxel_data["path_points"]:
        path_points = voxel_data["path_points"]
        original_x = [p["x"] for p in path_points]
        original_y = [p["y"] for p in path_points]
        original_z = [p["z"] for p in path_points]
        
        ax.plot(original_x, original_y, original_z,
               color='red', linewidth=3, alpha=0.7,
               label=f'原始A*路径 ({len(path_points)}点)', linestyle='--')
        original_path_shown = True
    
    # 3. 绘制Rust漏斗算法细化路径（绿色实线）
    refined_path_shown = False
    if "refined_path" in voxel_data and voxel_data["refined_path"]:
        refined_path_data = voxel_data["refined_path"]
        refined_points = refined_path_data["points"]
        
        refined_x = [p["x"] for p in refined_points]
        refined_y = [p["y"] for p in refined_points]
        refined_z = [p["z"] for p in refined_points]
        
        ax.plot(refined_x, refined_y, refined_z,
               color='green', linewidth=4, alpha=0.9,
               label=f'Rust漏斗算法路径 ({len(refined_points)}点)')
        
        # 标记细化路径的关键点
        ax.scatter(refined_x, refined_y, refined_z,
                  c='green', s=80, alpha=0.8, edgecolors='darkgreen', linewidth=1)
        
        refined_path_shown = True
        
        print(f"Rust漏斗算法路径点数: {len(refined_points)}")
        print(f"路径总长度: {refined_path_data['total_length']:.2f}")
        print(f"路径平滑度: {refined_path_data['smoothness']:.3f}")
    
    # 4. 标记起点和终点
    if start_point and end_point:
        ax.scatter([start_point[0]], [start_point[1]], [start_point[2]],
                  c='darkgreen', s=250, marker='^', alpha=0.9,
                  label='起点', edgecolors='black', linewidth=2)
        ax.scatter([end_point[0]], [end_point[1]], [end_point[2]],
                  c='darkred', s=250, marker='v', alpha=0.9,
                  label='终点', edgecolors='black', linewidth=2)
    
    # 设置坐标轴
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_zlabel("Z")
    ax.set_title("Rust漏斗算法路径细化结果")
    
    # 设置坐标轴范围
    ax.set_xlim(0, size_xy)
    ax.set_ylim(0, size_xy)
    ax.set_zlim(0, size_z)
    
    # 添加图例
    ax.legend(loc="upper right")
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    
    # 设置视角
    ax.view_init(elev=20, azim=45)
    
    # 设置背景色
    ax.xaxis.pane.fill = False
    ax.yaxis.pane.fill = False
    ax.zaxis.pane.fill = False
    
    # 打印路径对比信息
    print(f"\n=== 路径对比 ===")
    if original_path_shown and "path_points" in voxel_data:
        print(f"原始A*路径点数: {len(voxel_data['path_points'])}")
    if refined_path_shown and "refined_path" in voxel_data:
        refined_data = voxel_data["refined_path"]
        print(f"细化路径点数: {len(refined_data['points'])}")
        if original_path_shown:
            reduction = (1 - len(refined_data['points']) / len(voxel_data['path_points'])) * 100
            print(f"路径点减少: {reduction:.1f}%")
    
    return fig


def print_detailed_info(voxel_data):
    """打印详细信息"""
    print(f"\n=== 详细信息 ===")
    
    # 管道信息
    if "corridors" in voxel_data and voxel_data["corridors"]:
        corridor = voxel_data["corridors"][0]
        print(f"管道ID: {corridor['id']}")
        print(f"管道门数: {len(corridor['gates'])}")
        print(f"管道总长度: {corridor['total_length']:.2f}")
        print(f"最小截面积: {corridor['min_cross_section']:.2f}")
    
    # 门户信息
    if "corridor_gates" in voxel_data and voxel_data["corridor_gates"]:
        gates = voxel_data["corridor_gates"]
        print(f"总门户数: {len(gates)}")
        
        same_level = sum(1 for g in gates if g["gate_type"] == "SameLevel")
        cross_level = sum(1 for g in gates if g["gate_type"] == "CrossLevel")
        print(f"同级门户: {same_level}, 跨级门户: {cross_level}")
        
        avg_area = sum(g["effective_area"] for g in gates) / len(gates)
        print(f"平均门户面积: {avg_area:.2f}")
    
    # 路径信息
    if "refined_path" in voxel_data and voxel_data["refined_path"]:
        refined = voxel_data["refined_path"]
        print(f"细化路径长度: {refined['total_length']:.2f}")
        print(f"路径平滑度: {refined['smoothness']:.3f}")


def main():
    """主函数"""
    try:
        print("加载3D体素数据...")
        voxel_data = load_voxel_data("voxel_data_3d.json")
        
        # 打印详细信息
        print_detailed_info(voxel_data)
        
        # 创建Rust漏斗算法结果可视化
        print("\n创建Rust漏斗算法结果可视化...")
        fig = visualize_rust_funnel_result(voxel_data)
        
        if fig:
            # 保存图像
            plt.savefig("rust_funnel_algorithm_result.png", dpi=150, bbox_inches="tight")
            print("Rust漏斗算法结果图已保存为 rust_funnel_algorithm_result.png")
            
            # 显示交互式图形
            plt.show()
        else:
            print("无法创建可视化")
            
    except FileNotFoundError:
        print("错误: 找不到 voxel_data_3d.json 文件")
        print("请先运行Rust程序生成数据文件")
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
