#!/usr/bin/env python3
"""
3D切片可视化脚本
将3D数据按Z轴切片，生成类似2D可视化的PNG图像
每个切片显示：网格边框（绿色/红色）+ 障碍物填充（黑色）+ 透明背景
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle
import os


def load_voxel_data(filename):
    """加载体素数据"""
    with open(filename, "r") as f:
        data = json.load(f)
    return data


def create_voxel_array(voxel_data):
    """将一维体素数据转换为3D数组"""
    size_xy = voxel_data["size_xy"]
    size_z = voxel_data["size_z"]
    voxels_1d = voxel_data["voxels"]

    # 转换为3D数组 (z, y, x)
    voxels_3d = np.array(voxels_1d).reshape((size_z, size_xy, size_xy))

    return voxels_3d


def get_slice_boundaries(boundaries, z_level):
    """获取指定Z层的网格边界"""
    slice_boundaries = []

    for boundary in boundaries:
        z_start = boundary["z"]
        z_end = boundary["z"] + boundary["depth"]

        # 检查边界是否与当前Z层相交
        if z_start <= z_level < z_end:
            slice_boundaries.append(boundary)

    return slice_boundaries


def draw_grid_boundary_2d(ax, boundary, z_level):
    """在2D图上绘制网格边界矩形"""
    x = boundary["x"]
    y = boundary["y"]
    width = boundary["width"]
    height = boundary["height"]

    # 根据网格类型选择颜色
    if boundary["node_type"] == "Empty":
        color = "green"
        alpha = 0.8
        linewidth = 2
    elif boundary["node_type"] == "Blocked":
        # 被占据的网格：红色边框（但内部已经是黑色填充了，所以只画边框）
        color = "red"
        alpha = 0.8
        linewidth = 2
    else:  # Mixed - 理论上不应该存在
        print(f"警告: 发现Mixed类型的网格边界，这不应该发生！")
        return

    # 绘制矩形边框，不填充（因为被占据的网格内部已经通过体素显示为黑色了）
    rect = Rectangle(
        (x, y),
        width,
        height,
        linewidth=linewidth,
        edgecolor=color,
        facecolor="none",  # 不填充
        alpha=alpha,
    )
    ax.add_patch(rect)


def visualize_slice(voxel_data, z_level, path_points=None, save_path=None):
    """可视化指定Z层的切片"""
    voxels_3d = create_voxel_array(voxel_data)
    size_xy = voxel_data["size_xy"]

    # 获取当前Z层的数据
    slice_data = voxels_3d[z_level, :, :]

    # 创建图像
    fig, ax = plt.subplots(figsize=(10, 10))

    # 设置透明背景
    fig.patch.set_alpha(0)
    ax.patch.set_alpha(0)

    # 显示障碍物（黑色填充）
    obstacle_mask = slice_data == 3
    if np.any(obstacle_mask):
        # 创建障碍物图像
        obstacle_image = np.zeros((size_xy, size_xy, 4))  # RGBA
        obstacle_image[obstacle_mask] = [0, 0, 0, 1]  # 黑色不透明
        # 体素坐标对齐：每个体素代表[i, i+1) x [j, j+1)的区域
        ax.imshow(obstacle_image, origin="lower", extent=[0, size_xy, 0, size_xy])

    # 绘制网格边界
    boundaries = voxel_data["grid_boundaries"]
    slice_boundaries = get_slice_boundaries(boundaries, z_level)

    green_count = 0
    red_count = 0

    for boundary in slice_boundaries:
        if boundary["node_type"] == "Empty":
            draw_grid_boundary_2d(ax, boundary, z_level)
            green_count += 1
        elif boundary["node_type"] == "Blocked":
            draw_grid_boundary_2d(ax, boundary, z_level)
            red_count += 1

    # 绘制路径点
    if path_points:
        # 过滤出当前Z层的路径点
        current_z_points = [(x, y) for x, y, z in path_points if abs(z - z_level) < 0.5]

        if current_z_points:
            xs, ys = zip(*current_z_points)
            ax.scatter(
                xs,
                ys,
                c="orange",
                s=100,
                marker="o",
                alpha=0.8,
                edgecolors="darkorange",
                linewidth=2,
                label="粗路径点",
            )

            # 如果有多个点，连线
            if len(current_z_points) > 1:
                ax.plot(xs, ys, "orange", linewidth=3, alpha=0.6, label="粗路径连线")

    # 绘制细化路径点
    refined_path = voxel_data.get("refined_path")
    if refined_path and "points" in refined_path:
        refined_points = [(p["x"], p["y"], p["z"]) for p in refined_path["points"]]
        # 过滤出当前Z层的细化路径点
        current_z_refined_points = [
            (x, y) for x, y, z in refined_points if abs(z - z_level) < 0.5
        ]

        if current_z_refined_points:
            xs_refined, ys_refined = zip(*current_z_refined_points)
            ax.scatter(
                xs_refined,
                ys_refined,
                c="blue",
                s=60,
                marker="s",
                alpha=0.9,
                edgecolors="darkblue",
                linewidth=1,
                label="细化路径点",
            )

            # 如果有多个点，连线
            if len(current_z_refined_points) > 1:
                ax.plot(
                    xs_refined,
                    ys_refined,
                    "blue",
                    linewidth=2,
                    alpha=0.8,
                    label="细化路径连线",
                )

    # 设置坐标轴 - 对齐到整数网格
    ax.set_xlim(0, size_xy)
    ax.set_ylim(0, size_xy)
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_title(f"Z={z_level} 层切片 (绿色:{green_count}个, 红色:{red_count}个网格)")

    # 添加网格线，对齐到整数坐标
    ax.set_xticks(range(size_xy))
    ax.set_yticks(range(size_xy))
    ax.grid(True, alpha=0.3)
    ax.set_aspect("equal")

    # 添加图例
    from matplotlib.lines import Line2D

    legend_elements = [
        Line2D([0], [0], color="green", lw=2, label="空旷网格边框"),
        Line2D([0], [0], color="red", lw=2, label="被占据网格边框"),
        Line2D([0], [0], color="black", lw=4, label="障碍物"),
    ]

    # 如果有路径点，添加到图例
    if path_points and any(abs(z - z_level) < 0.5 for x, y, z in path_points):
        legend_elements.append(
            Line2D(
                [0],
                [0],
                marker="o",
                color="orange",
                lw=0,
                markersize=8,
                label="粗路径点",
            )
        )

    # 如果有细化路径点，添加到图例
    refined_path = voxel_data.get("refined_path")
    if refined_path and "points" in refined_path:
        refined_points = [(p["x"], p["y"], p["z"]) for p in refined_path["points"]]
        if any(abs(z - z_level) < 0.5 for x, y, z in refined_points):
            legend_elements.append(
                Line2D(
                    [0],
                    [0],
                    marker="s",
                    color="blue",
                    lw=0,
                    markersize=8,
                    label="细化路径点",
                )
            )

    ax.legend(handles=legend_elements, loc="upper right")

    plt.tight_layout()

    # 保存图像
    if save_path:
        plt.savefig(
            save_path, dpi=150, bbox_inches="tight", facecolor="white", edgecolor="none"
        )
        print(f"切片 Z={z_level} 已保存到 {save_path}")

    return fig


def create_all_slices(voxel_data, output_dir="slices"):
    """创建所有Z层的切片图像"""
    size_xy = voxel_data["size_xy"]
    size_z = voxel_data["size_z"]

    # 创建输出目录
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    print(f"开始生成 {size_z} 个切片图像...")

    # 获取路径点数据
    path_points = None
    if "path_points" in voxel_data and voxel_data["path_points"]:
        path_points = [(p["x"], p["y"], p["z"]) for p in voxel_data["path_points"]]
        print(f"找到路径点: {len(path_points)} 个")

    # 统计每层的障碍物数量
    voxels_3d = create_voxel_array(voxel_data)

    for z in range(size_z):
        slice_data = voxels_3d[z, :, :]
        obstacle_count = np.sum(slice_data == 3)

        # 只为有内容的层生成图像（有障碍物或网格边界）
        boundaries = voxel_data["grid_boundaries"]
        slice_boundaries = get_slice_boundaries(boundaries, z)

        if obstacle_count > 0 or len(slice_boundaries) > 0:
            save_path = os.path.join(output_dir, f"slice_z_{z:02d}.png")
            fig = visualize_slice(voxel_data, z, path_points, save_path)
            plt.close(fig)  # 关闭图像以节省内存
        else:
            print(f"跳过空白层 Z={z}")

    print(f"所有切片已保存到 {output_dir} 目录")


def create_summary_grid(voxel_data, output_path="slices_summary.png"):
    """创建所有切片的缩略图网格"""
    size_xy = voxel_data["size_xy"]
    size_z = voxel_data["size_z"]
    voxels_3d = create_voxel_array(voxel_data)

    # 获取路径点数据
    path_points = None
    if "path_points" in voxel_data and voxel_data["path_points"]:
        path_points = [(p["x"], p["y"], p["z"]) for p in voxel_data["path_points"]]
        print(f"总览图中包含路径点: {len(path_points)} 个")

    # 计算网格布局
    cols = 8  # 每行8个切片
    rows = (size_z + cols - 1) // cols

    fig, axes = plt.subplots(rows, cols, figsize=(20, rows * 2.5))
    fig.suptitle(f"所有Z层切片总览 ({size_xy}x{size_xy}x{size_z})", fontsize=16)

    if rows == 1:
        axes = [axes]

    for z in range(size_z):
        row = z // cols
        col = z % cols

        if rows > 1:
            ax = axes[row][col]
        else:
            ax = axes[col]

        slice_data = voxels_3d[z, :, :]

        # 显示障碍物
        obstacle_mask = slice_data == 3
        if np.any(obstacle_mask):
            obstacle_image = np.zeros((size_xy, size_xy, 4))
            obstacle_image[obstacle_mask] = [0, 0, 0, 1]
            ax.imshow(obstacle_image, origin="lower", extent=[0, size_xy, 0, size_xy])

        # 绘制网格边界（简化版）
        boundaries = voxel_data["grid_boundaries"]
        slice_boundaries = get_slice_boundaries(boundaries, z)

        for boundary in slice_boundaries:
            if boundary["node_type"] == "Empty":
                color = "green"
            elif boundary["node_type"] == "Blocked":
                color = "red"
            else:
                continue

            rect = Rectangle(
                (boundary["x"], boundary["y"]),
                boundary["width"],
                boundary["height"],
                linewidth=1,
                edgecolor=color,
                facecolor="none",
                alpha=0.6,
            )
            ax.add_patch(rect)

        # 不在切片总览中显示路径点

        ax.set_title(f"Z={z}", fontsize=10)
        ax.set_xlim(0, size_xy)
        ax.set_ylim(0, size_xy)
        ax.set_xticks([])
        ax.set_yticks([])

    # 隐藏多余的子图
    for z in range(size_z, rows * cols):
        row = z // cols
        col = z % cols
        if rows > 1:
            axes[row][col].set_visible(False)
        else:
            axes[col].set_visible(False)

    plt.tight_layout()
    plt.savefig(output_path, dpi=150, bbox_inches="tight")
    print(f"切片总览已保存到 {output_path}")

    return fig


def visualize_3d_path(voxel_data, output_path="3d_path_visualization.png"):
    """创建3D路径可视化 - 三个视角"""
    has_coarse_path = "path_points" in voxel_data and voxel_data["path_points"]
    has_refined_path = (
        "refined_path" in voxel_data
        and voxel_data["refined_path"]
        and "points" in voxel_data["refined_path"]
    )

    if not has_coarse_path and not has_refined_path:
        print("没有找到路径点数据")
        return None

    path_points = []
    refined_points = []

    if has_coarse_path:
        path_points = [(p["x"], p["y"], p["z"]) for p in voxel_data["path_points"]]

    if has_refined_path:
        refined_points = [
            (p["x"], p["y"], p["z"]) for p in voxel_data["refined_path"]["points"]
        ]

    size_xy = voxel_data["size_xy"]
    size_z = voxel_data["size_z"]

    print(
        f"创建3D路径可视化，包含 {len(path_points)} 个粗路径点，{len(refined_points)} 个细化路径点"
    )

    # 创建三个子图
    fig = plt.figure(figsize=(18, 6))

    # 视角配置：(elevation, azimuth, title)
    views = [
        (0, 0, "X视角 (YZ平面)"),  # 从X轴正方向看
        (0, 90, "Y视角 (XZ平面)"),  # 从Y轴正方向看
        (90, 0, "Z视角 (XY平面)"),  # 从Z轴正方向看
    ]

    # 获取障碍物数据（只计算一次）
    voxels_3d = create_voxel_array(voxel_data)
    obstacle_voxels = voxels_3d == 3
    obstacle_voxels_transposed = None
    if np.any(obstacle_voxels):
        obstacle_voxels_transposed = np.transpose(obstacle_voxels, (2, 1, 0))

    for i, (elev, azim, view_title) in enumerate(views):
        ax = fig.add_subplot(1, 3, i + 1, projection="3d")

        # 设置透明背景
        ax.xaxis.pane.fill = False
        ax.yaxis.pane.fill = False
        ax.zaxis.pane.fill = False

        # 显示障碍物（可选，半透明）
        if obstacle_voxels_transposed is not None:
            ax.voxels(
                obstacle_voxels_transposed,
                facecolors="black",
                alpha=0.2,
                edgecolors="gray",
                linewidth=0.1,
            )

        # 绘制粗路径线
        if len(path_points) > 1:
            xs, ys, zs = zip(*path_points)
            ax.plot(xs, ys, zs, "orange", linewidth=3, alpha=0.8, label="粗路径")

        # 绘制粗路径点
        if path_points:
            xs, ys, zs = zip(*path_points)
            ax.scatter(
                xs,
                ys,
                zs,
                c="orange",
                s=60,
                marker="o",
                alpha=0.9,
                edgecolors="darkorange",
                linewidth=1,
                label="粗路径点",
            )

        # 绘制细化路径线
        if len(refined_points) > 1:
            xs_r, ys_r, zs_r = zip(*refined_points)
            ax.plot(xs_r, ys_r, zs_r, "blue", linewidth=2, alpha=0.9, label="细化路径")

        # 绘制细化路径点
        if refined_points:
            xs_r, ys_r, zs_r = zip(*refined_points)
            ax.scatter(
                xs_r,
                ys_r,
                zs_r,
                c="blue",
                s=40,
                marker="s",
                alpha=0.9,
                edgecolors="darkblue",
                linewidth=1,
                label="细化路径点",
            )

        # 标注起点和终点（优先使用细化路径，否则使用粗路径）
        points_for_endpoints = refined_points if refined_points else path_points
        if len(points_for_endpoints) >= 2:
            # 起点 - 绿色
            start_point = points_for_endpoints[0]
            ax.scatter(
                [start_point[0]],
                [start_point[1]],
                [start_point[2]],
                c="green",
                s=120,
                marker="s",
                alpha=1.0,
                edgecolors="darkgreen",
                linewidth=2,
                label="起点",
            )

            # 终点 - 红色
            end_point = points_for_endpoints[-1]
            ax.scatter(
                [end_point[0]],
                [end_point[1]],
                [end_point[2]],
                c="red",
                s=120,
                marker="^",
                alpha=1.0,
                edgecolors="darkred",
                linewidth=2,
                label="终点",
            )

        # 设置坐标轴
        ax.set_xlabel("X")
        ax.set_ylabel("Y")
        ax.set_zlabel("Z")
        ax.set_title(view_title)

        # 设置坐标轴范围
        ax.set_xlim(0, size_xy)
        ax.set_ylim(0, size_xy)
        ax.set_zlim(0, size_z)

        # 设置视角
        ax.view_init(elev=elev, azim=azim)

        # 添加图例（只在第一个子图添加）
        if i == 0:
            ax.legend(loc="upper right", fontsize=8)

        # 添加网格
        ax.grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图像
    plt.savefig(output_path, dpi=150, bbox_inches="tight")
    print(f"3D路径可视化已保存到 {output_path}")

    return fig


def main():
    """主函数"""
    try:
        print("加载3D体素数据...")
        voxel_data = load_voxel_data("voxel_data_3d.json")

        size_xy = voxel_data["size_xy"]
        size_z = voxel_data["size_z"]
        print(f"数据大小: {size_xy}x{size_xy}x{size_z}")
        print(f"网格边界数量: {len(voxel_data['grid_boundaries'])}")

        # 统计体素类型
        voxels = np.array(voxel_data["voxels"])
        obstacle_count = np.sum(voxels == 3)
        print(f"障碍物体素数量: {obstacle_count}")

        # 生成切片总览
        create_summary_grid(voxel_data)

        # 生成3D路径可视化
        if "path_points" in voxel_data and voxel_data["path_points"]:
            path_fig = visualize_3d_path(voxel_data)
            plt.figure(path_fig.number)  # 激活路径图

        # 显示所有图形
        # plt.show()

        print("\n可视化完成！")
        print("- 切片总览图保存为 slices_summary.png")
        if "path_points" in voxel_data and voxel_data["path_points"]:
            print("- 3D路径可视化保存为 3d_path_visualization.png")

    except FileNotFoundError:
        print("错误: 找不到 voxel_data_3d.json 文件")
        print("请先运行Rust程序生成数据文件")
    except Exception as e:
        print(f"错误: {e}")


if __name__ == "__main__":
    main()
