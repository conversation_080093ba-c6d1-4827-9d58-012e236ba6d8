#!/usr/bin/env python3
"""
漏斗算法实现
用于在管道内细化路径，生成平滑的飞行路径
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection


def load_voxel_data(filename):
    """加载体素数据"""
    with open(filename, "r") as f:
        data = json.load(f)
    return data


class Point3D:
    def __init__(self, x, y, z):
        self.x = x
        self.y = y
        self.z = z

    def __sub__(self, other):
        return Point3D(self.x - other.x, self.y - other.y, self.z - other.z)

    def __add__(self, other):
        return Point3D(self.x + other.x, self.y + other.y, self.z + other.z)

    def __mul__(self, scalar):
        return Point3D(self.x * scalar, self.y * scalar, self.z * scalar)

    def dot(self, other):
        return self.x * other.x + self.y * other.y + self.z * other.z

    def cross(self, other):
        return Point3D(
            self.y * other.z - self.z * other.y,
            self.z * other.x - self.x * other.z,
            self.x * other.y - self.y * other.x,
        )

    def length(self):
        return np.sqrt(self.x**2 + self.y**2 + self.z**2)

    def normalize(self):
        l = self.length()
        if l > 0:
            return Point3D(self.x / l, self.y / l, self.z / l)
        return Point3D(0, 0, 0)

    def to_list(self):
        return [self.x, self.y, self.z]


class FunnelAlgorithm:
    def __init__(self):
        self.gates = []
        self.path = []

    def add_gate(self, left_point, right_point, center_point):
        """添加一个门户（左边界点、右边界点、中心点）"""
        self.gates.append(
            {
                "left": Point3D(left_point[0], left_point[1], left_point[2]),
                "right": Point3D(right_point[0], right_point[1], right_point[2]),
                "center": Point3D(center_point[0], center_point[1], center_point[2]),
            }
        )

    def is_point_left_of_line(self, point, line_start, line_end):
        """判断点是否在线段的左侧（3D版本）"""
        # 使用叉积判断
        v1 = line_end - line_start
        v2 = point - line_start
        cross = v1.cross(v2)
        # 在3D中，我们需要考虑主要的投影平面
        return cross.z > 0  # 简化版本，假设主要在XY平面

    def line_intersect_plane(self, line_start, line_end, plane_point, plane_normal):
        """计算直线与平面的交点"""
        line_dir = line_end - line_start

        # 检查直线是否平行于平面
        denom = line_dir.dot(plane_normal)
        if abs(denom) < 1e-6:
            return None

        # 计算交点参数
        t = (plane_point - line_start).dot(plane_normal) / denom

        # 计算交点
        intersection = line_start + line_dir * t
        return intersection

    def point_in_gate(self, point, gate):
        """检查点是否在门户内部"""
        # 简化版本：检查点是否在门户的边界框内
        vertices = [gate["left"], gate["right"]]

        # 计算门户的边界框
        min_x = min(v.x for v in vertices)
        max_x = max(v.x for v in vertices)
        min_y = min(v.y for v in vertices)
        max_y = max(v.y for v in vertices)
        min_z = min(v.z for v in vertices)
        max_z = max(v.z for v in vertices)

        # 扩展边界框以确保包含门户区域
        margin = 0.5
        return (
            min_x - margin <= point.x <= max_x + margin
            and min_y - margin <= point.y <= max_y + margin
            and min_z - margin <= point.z <= max_z + margin
        )

    def compute_funnel_path(self, start_point, end_point):
        """计算漏斗路径 - 确保路径穿过每个门户"""
        if not self.gates:
            return [start_point, end_point]

        start = Point3D(start_point[0], start_point[1], start_point[2])
        end = Point3D(end_point[0], end_point[1], end_point[2])

        path = [start]
        current_pos = start

        # 逐个门户进行路径规划
        for i, gate in enumerate(self.gates):
            gate_center = gate["center"]

            # 下一个目标点
            if i < len(self.gates) - 1:
                next_target = self.gates[i + 1]["center"]
            else:
                next_target = end

            # 计算通过当前门户到下一个目标的最优路径点
            # 简化版本：使用门户中心点作为必经点
            path.append(gate_center)
            current_pos = gate_center

        # 添加终点
        path.append(end)

        # 进一步优化路径：在保证穿过门户的前提下，尽可能直线化
        optimized_path = self.optimize_path_through_gates(path)

        return [p.to_list() for p in optimized_path]

    def optimize_path_through_gates(self, path):
        """优化路径，在保证穿过门户的前提下尽可能直线化"""
        if len(path) <= 2:
            return path

        optimized = [path[0]]  # 起点

        i = 0
        while i < len(path) - 1:
            current = path[i]

            # 尝试跳过中间点，直接连接到更远的点
            furthest_reachable = i + 1

            for j in range(i + 2, len(path)):
                # 检查从current到path[j]的直线是否可行
                if self.is_path_valid(current, path[j], i + 1, j - 1):
                    furthest_reachable = j
                else:
                    break

            # 添加能到达的最远点
            if furthest_reachable < len(path):
                optimized.append(path[furthest_reachable])
                i = furthest_reachable
            else:
                break

        # 确保终点被包含
        if optimized[-1] != path[-1]:
            optimized.append(path[-1])

        return optimized

    def is_path_valid(self, start, end, gate_start_idx, gate_end_idx):
        """检查从start到end的直线路径是否穿过所有必要的门户"""
        # 简化版本：检查路径是否与中间的门户相交
        for i in range(gate_start_idx, gate_end_idx + 1):
            if i < len(self.gates):
                gate = self.gates[i]
                # 检查直线是否穿过门户
                if not self.line_passes_through_gate(start, end, gate):
                    return False
        return True

    def line_passes_through_gate(self, line_start, line_end, gate):
        """检查直线是否穿过门户"""
        # 简化版本：检查直线是否接近门户中心
        gate_center = gate["center"]

        # 计算直线上最接近门户中心的点
        line_dir = line_end - line_start
        line_length = line_dir.length()

        if line_length < 1e-6:
            return False

        line_dir = line_dir.normalize()

        # 投影门户中心到直线上
        to_gate = gate_center - line_start
        projection_length = to_gate.dot(line_dir)

        # 确保投影点在线段内
        if projection_length < 0 or projection_length > line_length:
            return False

        # 计算最近点
        closest_point = line_start + line_dir * projection_length

        # 检查距离是否足够近
        distance = (gate_center - closest_point).length()
        threshold = 1.0  # 可调整的阈值

        return distance <= threshold


def extract_gates_from_corridor(voxel_data):
    """从管道数据中提取门户信息"""
    if "corridors" not in voxel_data or not voxel_data["corridors"]:
        return None, None, None

    corridor = voxel_data["corridors"][0]
    corridor_gates = voxel_data.get("corridor_gates", [])

    gates = []
    gate_ids = corridor["gates"]

    for gate_id in gate_ids:
        if gate_id < len(corridor_gates):
            gate = corridor_gates[gate_id]
            shared_face = gate["shared_face"]
            vertices = shared_face["vertices"]

            if len(vertices) >= 4:
                # 简化：使用矩形的对角顶点作为左右边界
                v1 = vertices[0]
                v3 = vertices[2]  # 对角顶点
                center = gate["center_point"]

                left_point = [v1["x"], v1["y"], v1["z"]]
                right_point = [v3["x"], v3["y"], v3["z"]]
                center_point = [center["x"], center["y"], center["z"]]

                gates.append(
                    {
                        "left": left_point,
                        "right": right_point,
                        "center": center_point,
                        "vertices": vertices,
                    }
                )

    start_point = corridor["start_point"]
    end_point = corridor["end_point"]

    start = [start_point["x"], start_point["y"], start_point["z"]]
    end = [end_point["x"], end_point["y"], end_point["z"]]

    return gates, start, end


def create_gate_rectangle(vertices, scale=0.8):
    """创建门户矩形"""
    if len(vertices) >= 4:
        # 计算中心点
        center_x = sum(v["x"] for v in vertices) / len(vertices)
        center_y = sum(v["y"] for v in vertices) / len(vertices)
        center_z = sum(v["z"] for v in vertices) / len(vertices)

        # 缩放顶点
        scaled_vertices = []
        for v in vertices:
            scaled_x = center_x + (v["x"] - center_x) * scale
            scaled_y = center_y + (v["y"] - center_y) * scale
            scaled_z = center_z + (v["z"] - center_z) * scale
            scaled_vertices.append([scaled_x, scaled_y, scaled_z])

        return scaled_vertices
    return None


def visualize_funnel_comparison(voxel_data):
    """可视化漏斗算法前后的路径对比"""
    fig = plt.figure(figsize=(15, 10))
    ax = fig.add_subplot(111, projection="3d")

    size_xy = voxel_data["size_xy"]
    size_z = voxel_data["size_z"]

    # 提取门户和路径信息
    gates, start_point, end_point = extract_gates_from_corridor(voxel_data)

    if not gates:
        print("没有找到门户数据")
        return None

    print(f"找到 {len(gates)} 个门户")

    # 1. 绘制门户（浅蓝色矩形）
    gate_rectangles = []
    for gate in gates:
        rect_vertices = create_gate_rectangle(gate["vertices"])
        if rect_vertices:
            gate_rectangles.append(rect_vertices)

    if gate_rectangles:
        gate_collection = Poly3DCollection(
            gate_rectangles,
            facecolors="lightblue",
            edgecolors="blue",
            alpha=0.6,
            linewidths=1,
        )
        ax.add_collection3d(gate_collection)

    # 2. 绘制原始路径（粗路径）
    if "path_points" in voxel_data and voxel_data["path_points"]:
        path_points = voxel_data["path_points"]
        original_x = [p["x"] for p in path_points]
        original_y = [p["y"] for p in path_points]
        original_z = [p["z"] for p in path_points]

        ax.plot(
            original_x,
            original_y,
            original_z,
            color="red",
            linewidth=3,
            alpha=0.7,
            label="原始A*路径",
            linestyle="--",
        )

    # 3. 应用漏斗算法
    funnel = FunnelAlgorithm()
    for gate in gates:
        funnel.add_gate(gate["left"], gate["right"], gate["center"])

    refined_path = funnel.compute_funnel_path(start_point, end_point)

    # 4. 绘制细化后的路径
    if len(refined_path) >= 2:
        refined_x = [p[0] for p in refined_path]
        refined_y = [p[1] for p in refined_path]
        refined_z = [p[2] for p in refined_path]

        ax.plot(
            refined_x,
            refined_y,
            refined_z,
            color="green",
            linewidth=3,
            alpha=0.9,
            label="漏斗算法细化路径",
        )

        # 标记细化路径的关键点
        ax.scatter(refined_x, refined_y, refined_z, c="green", s=50, alpha=0.8)

    # 5. 标记起点和终点
    ax.scatter(
        [start_point[0]],
        [start_point[1]],
        [start_point[2]],
        c="darkgreen",
        s=200,
        marker="^",
        alpha=0.9,
        label="起点",
        edgecolors="black",
        linewidth=2,
    )
    ax.scatter(
        [end_point[0]],
        [end_point[1]],
        [end_point[2]],
        c="darkred",
        s=200,
        marker="v",
        alpha=0.9,
        label="终点",
        edgecolors="black",
        linewidth=2,
    )

    # 设置坐标轴
    ax.set_xlabel("X")
    ax.set_ylabel("Y")
    ax.set_zlabel("Z")
    ax.set_title("漏斗算法路径细化对比")

    # 设置坐标轴范围
    ax.set_xlim(0, size_xy)
    ax.set_ylim(0, size_xy)
    ax.set_zlim(0, size_z)

    # 添加图例
    ax.legend(loc="upper right")

    # 添加网格
    ax.grid(True, alpha=0.3)

    # 设置视角
    ax.view_init(elev=20, azim=45)

    # 设置背景色
    ax.xaxis.pane.fill = False
    ax.yaxis.pane.fill = False
    ax.zaxis.pane.fill = False

    # 打印路径信息
    print(f"\n=== 路径对比 ===")
    if "path_points" in voxel_data and voxel_data["path_points"]:
        print(f"原始A*路径点数: {len(voxel_data['path_points'])}")
    print(f"漏斗算法路径点数: {len(refined_path)}")

    return fig


def main():
    """主函数"""
    try:
        print("加载3D体素数据...")
        voxel_data = load_voxel_data("voxel_data_3d.json")

        # 创建漏斗算法路径对比可视化
        print("\n创建漏斗算法路径对比可视化...")
        fig = visualize_funnel_comparison(voxel_data)

        if fig:
            # 保存图像
            plt.savefig("funnel_algorithm_comparison.png", dpi=150, bbox_inches="tight")
            print("漏斗算法对比图已保存为 funnel_algorithm_comparison.png")

            # 显示交互式图形
            plt.show()
        else:
            print("无法创建可视化")

    except FileNotFoundError:
        print("错误: 找不到 voxel_data_3d.json 文件")
        print("请先运行Rust程序生成数据文件")
    except Exception as e:
        print(f"错误: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
